# This workflow will do a clean installation of node dependencies, run the landing page Gatsby app, wait until
# <PERSON>lify has pushed a deployment preview, and then perform a Lighthouse CI check on it.

name: Lighthouse CI

on:
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
      - name: Install & Build
        run: |
          npm install
          npm run build
        env: ${{ secrets }}  # Pass all secrets as environment variables
      - name: Wait for Netlify
        uses: JakePartusch/wait-for-netlify-action@v1.3
        id: netlify
        with:
          site_name: "dazzling-lovelace-19c3a1"
      - name: Audit URLs using Lighthouse
        uses: treosh/lighthouse-ci-action@v9
        with:
          urls: ${{ steps.netlify.outputs.url }}
          configPath: "./lighthouserc.json"
          uploadArtifacts: true
          runs: 3
