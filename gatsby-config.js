require("dotenv").config()

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === "development"

module.exports = {
  trailingSlash: "always",
  siteMetadata: {
    title: `Investing made easy`,
    author: `Wealthyhood`,
    description: `Stocks, ETFs and high interest with zero commissions and powerful automation. Start building wealth today!`,
    siteUrl: `https://wealthyhood.com`,
    image: `https://wealthyhood.com/img/preview-image.png`,
    social: {
      twitter: `@wealthyhood_`,
      image: `https://wealthyhood.com/img/preview-image.png`,
    },
  },
  plugins: [
    {
      resolve: `gatsby-plugin-netlify`,
      options: {
        generateMatchPathRewrites: false,
      },
    },
    {
      resolve: "@sentry/gatsby",
      options: {
        dsn: process.env.SENTRY_DSN,
        tracesSampleRate: 0.1,
      },
    },
    {
      resolve: `gatsby-source-filesystem`,
      options: {
        path: `${__dirname}/content/${isDevelopment ? "learn-app-dev" : "learn-app"}`,
        name: `learn-app`,
      },
    },
    {
      resolve: `gatsby-source-filesystem`,
      options: {
        path: `${__dirname}/content/pages`,
        name: `pages`,
      },
    },
    {
      resolve: `gatsby-source-filesystem`,
      options: {
        path: `${__dirname}/content/press-releases`,
        name: `press-releases`,
      },
    },
    {
      resolve: `gatsby-source-filesystem`,
      options: {
        path: `${__dirname}/content/assets`,
        name: `assets`,
      },
    },
    {
      resolve: `gatsby-transformer-remark`,
      options: {
        plugins: [
          {
            resolve: `gatsby-remark-images`,
            options: {
              maxWidth: 590,
              linkImagesToOriginal: false, // Prevent linking images to the original file
              quality: 80, // Reduce quality to optimize load times
              withWebp: true, // Generate WebP versions of images for better performance
              tracedSVG: true, // Adds a traced SVG placeholder for better user experience
            },
          },

          {
            resolve: `gatsby-remark-responsive-iframe`,
            options: {
              wrapperStyle: `margin-bottom: 1.0725rem`,
            },
          },
          `gatsby-remark-prismjs`,
          `gatsby-remark-copy-linked-files`,
          `gatsby-remark-smartypants`,
        ],
      },
    },
    `gatsby-plugin-image`,
    `gatsby-transformer-sharp`,
    `gatsby-plugin-sharp`,
    {
      resolve: "gatsby-plugin-google-tagmanager",
      options: {
        id: "GTM-PJHF35G",

        // Include GTM in development.
        // Defaults to false meaning GTM will only be loaded in production.
        includeInDevelopment: false,

        // datalayer to be set before GTM is loaded
        // should be an object or a function that is executed in the browser
        // Defaults to null
        defaultDataLayer: { platform: "gatsby" },
      },
    },
    {
      resolve: `gatsby-plugin-manifest`,
      options: {
        name: `Wealthyhood Investing Platform`,
        short_name: `Wealthyhood`,
        start_url: `/`,
        background_color: `#ffffff`,
        theme_color: `#663399`,
        display: `minimal-ui`,
        icon: `content/assets/logo-icon.png`,
      },
    },
    {
      resolve: "gatsby-plugin-offline",
      options: {
        workboxConfig: {
          runtimeCaching: [
            {
              // Use StaleWhileRevalidate for all non-CSS files
              urlPattern: /^(?!.*\.css$).*$/,
              handler: "StaleWhileRevalidate",
            },
            {
              // Use NetworkOnly for CSS files - never use cache
              urlPattern: /\.css$/,
              handler: "NetworkOnly",
            },
          ],
        },
      },
    },
    `gatsby-plugin-react-helmet`,
    ...(!isDevelopment
      ? [
          {
            resolve: `gatsby-plugin-sitemap`,
            options: {
              excludes: [
                `/affiliate-dashboard`,
                `/ambassador-dashboard`,
                `/manage-ambassadors`,
                `/referral-dashboard`,
              ],
            },
          },
        ]
      : []),
    {
      resolve: "gatsby-plugin-web-font-loader",
      options: {
        google: {
          families: ["Poppins:400", "Work Sans:600,700&display=swap"],
        },
      },
    },
    {
      resolve: "gatsby-source-contentful",
      options: {
        spaceId: process.env.CONTENTFUL_SPACE_ID,
        accessToken:
          process.env.NODE_ENV === "development"
            ? process.env.CONTENTFUL_PREVIEW_ACCESS_TOKEN
            : process.env.CONTENTFUL_DELIVERY_ACCESS_TOKEN,
        host:
          process.env.NODE_ENV === "development"
            ? "preview.contentful.com"
            : undefined,
        downloadLocal: false,
      },
    },
  ].filter(Boolean), // Filter out any falsey values
}
