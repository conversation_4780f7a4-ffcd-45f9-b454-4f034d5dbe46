export default async (request, context) => {
  // Get the user's country from Netlify's geo information
  const country = context.geo?.country?.code || "UNKNOWN"

  const response = await context.next()

  if (country === "GR") {
    response.headers.set("X-User-Region", "gr")
  } else if (country === "GB") {
    response.headers.set("X-User-Region", "gb")
  } else {
    response.headers.set("X-User-Region", "eu")
  }

  return response
}
