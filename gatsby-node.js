const slugify = require("./src/utils/slugify")
const fs = require("fs")
const path = require(`path`)
const { createFilePath } = require(`gatsby-source-filesystem`)
const LocaleEnum = require("./src/configs/locale-config")
const {
  SUPPORTED_LANGUAGE_REGION_PAIRS,
  REGION_TO_DEFAULT_LOCALE,
  REGION_TO_DEFAULT_LANGUAGE,
  LanguageEnum,
} = require("./src/configs/language-region-config")
const { parseStringPromise, Builder } = require("xml2js")
const axios = require("axios")
const siteURL = "https://wealthyhood.com/"
const isDevelopment = process.env.NODE_ENV === "development"
const webpack = require("webpack")
const RedisService = require("./utils/redisService")
const { execSync } = require("child_process")

const ensureLeadingTrailingSlash = (url) => {
  // Ensure the URL starts & ends with a slash
  let finalUrl = url.endsWith("/") ? url : `${url}/`
  return finalUrl.startsWith("/") ? finalUrl : `/${finalUrl}`
}

const logActivity = (reporter, activity, fn) => {
  const activityTimer = reporter.activityTimer(activity)
  activityTimer.start()

  return fn()
    .then(() => {
      activityTimer.end()
    })
    .catch((err) => {
      activityTimer.end()
      throw err
    })
}

exports.sourceNodes = async ({
  actions,
  createNodeId,
  createContentDigest,
  reporter,
}) => {
  const { createNode } = actions

  // Fetch data from the edge server
  const fetchData = async () => {
    reporter.info("Fetching app config from edge server.")

    try {
      const response = await axios.get(
        `https://mobile-config-edge-server.wealthyhood.workers.dev/app-config`
      )
      return response.data
    } catch (error) {
      reporter.panicOnBuild(`Error while fetching app config from edge server.`)
      throw error
    }
  }

  await logActivity(reporter, "Sourcing nodes", async () => {
    reporter.verbose("Starting to source nodes...")

    // Fetch the data
    const configData = await fetchData()

    // Create node for build-time data
    createNode({
      // Data for the node
      id: createNodeId(`app-config`),
      parent: null,
      children: [],
      internal: {
        type: `AppConfig`,
        contentDigest: createContentDigest(configData),
      },
      ...configData,
    })

    reporter.verbose("Finished sourcing nodes")
  })
}

exports.createPages = async ({ actions, graphql, reporter }) => {
  reporter.verbose("Starting to create pages...")

  await Promise.all([
    createMainPages(graphql, actions, reporter),
    createAssetPages(graphql, actions, reporter),
    createPagesFromMarkdown(graphql, actions, reporter),
    createPressReleasePages(graphql, actions, reporter),
    createPressReleaseDirectoryPages(graphql, actions, reporter),
    createLearnDirectory(actions, reporter),
    createHelpCentrePages(graphql, actions, reporter),
    createGlossaryPages(graphql, actions, reporter),
    createFinancialReportsPages(actions, reporter),
  ])

  if (isDevelopment) {
    reporter.info(
      "Skipping non-asset custom page creation in development mode."
    )
    return
  }

  await Promise.all([
    createLegalPages(graphql, actions, reporter),
    createBlogPages(graphql, actions, reporter),
    createAuthorPages(graphql, actions, reporter),
    createCategoryPages(graphql, actions, reporter),
  ])
}

exports.onCreateNode = ({ node, actions, getNode }) => {
  const { createNodeField } = actions

  /**
   * Create markdown nodes for file-system based content only.
   */
  if (node.internal.type === `MarkdownRemark` && node.fileAbsolutePath) {
    const value = createFilePath({ node, getNode })
    createNodeField({
      name: `slug`,
      node,
      value,
    })
  }
}

exports.onPreInit = ({ reporter }) => {
  reporter.verbose("🟢 Initial Gatsby setup starting...")
  reporter.verbose(`Environment: ${process.env.NODE_ENV}`)
}

exports.onPreBootstrap = ({ reporter }) => {
  reporter.verbose("Running onPreBootstrap")
}

exports.onCreateWebpackConfig = ({ stage, actions, getConfig, reporter }) => {
  const config = getConfig()
  reporter.verbose(`Webpack stage: ${stage}`)
  reporter.verbose(`Entry points: ${JSON.stringify(config.entry)}`)

  actions.setWebpackConfig({
    devtool: isDevelopment ? "eval-cheap-module-source-map" : false,
    resolve: {
      fallback: {
        crypto: false,
        stream: false,
      },
    },
    plugins: [
      new webpack.DefinePlugin({
        __CSS_VERSION__: JSON.stringify(require("./package.json").version),
      }),
      new webpack.ProgressPlugin((percentage, message, ...args) => {
        // Convert to percentage
        const percent = (percentage * 100).toFixed(2)

        // Get the current module being processed
        const moduleInfo = args[0] ? `Module: ${args[0]}` : ""

        // Format the active modules count if available
        const activeModules = args[1] ? `Active: ${args[1]}` : ""

        if (isDevelopment) {
          reporter.info(
            `[${stage}] ${percent}% - ${message} ${moduleInfo} ${activeModules}`
          )
        }
      }),
    ],
  })
}

const updateSitemapWithAlternateLinks = async (graphql, reporter) => {
  const blogPostsResults = await graphql(`
    {
      allContentfulPostsBlog {
        nodes {
          slug
          locale {
            code
          }
          category {
            name
            slug
          }
          hrefLangs {
            url
            languageCode
          }
        }
      }
    }
  `)

  if (blogPostsResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const blogPostByUrlDict =
    blogPostsResults.data.allContentfulPostsBlog.nodes.reduce(
      (blogPostDict, post) => {
        const url = `${siteURL}${post.locale.code}/blog/${post.category.slug}/${post.slug}/`
        blogPostDict[url] = post
        return blogPostDict
      },
      {}
    )

  const sitemapPath = "./public/sitemap-0.xml" // Adjust the path to your sitemap
  try {
    const sitemapXML = fs.readFileSync(sitemapPath, "utf8")
    const sitemapObj = await parseStringPromise(sitemapXML)

    /**
     * Update sitemap with alternate links for:
     *  - UK and EL blog
     *  - UK and EL blog directory
     *  - Blog posts with hrefLangs
     */
    sitemapObj.urlset.url.forEach((page) => {
      const loc = page.loc[0]

      if (loc === `${siteURL}uk/blog/` || loc === `${siteURL}el/blog/`) {
        page["xhtml:link"] = [
          {
            $: { rel: "alternate", hreflang: "en", href: `${siteURL}uk/blog/` },
          },
          {
            $: { rel: "alternate", hreflang: "el", href: `${siteURL}el/blog/` },
          },
          {
            $: {
              rel: "alternate",
              hreflang: "x-default",
              href: `${siteURL}uk/blog/`,
            },
          },
        ]
        return
      }

      if (loc === `${siteURL}uk/blog/directory/`) {
        page["xhtml:link"] = [
          {
            $: {
              rel: "alternate",
              hreflang: "en",
              href: `${siteURL}uk/blog/directory/`,
            },
          },
          {
            $: {
              rel: "alternate",
              hreflang: "el",
              href: `${siteURL}el/blog/directory/`,
            },
          },
          {
            $: {
              rel: "alternate",
              hreflang: "x-default",
              href: `${siteURL}uk/blog/directory/`,
            },
          },
        ]
        return
      }

      // If a matching post is found and it has hrefLangs
      const blogPostMatchingSitemapEntry =
        blogPostByUrlDict[ensureLeadingTrailingSlash(loc)]
      if (
        blogPostMatchingSitemapEntry &&
        blogPostMatchingSitemapEntry.hrefLangs &&
        blogPostMatchingSitemapEntry.hrefLangs.length
      ) {
        page["xhtml:link"] = blogPostMatchingSitemapEntry.hrefLangs.map(
          (lang) => ({
            $: {
              rel: "alternate",
              hreflang: lang.languageCode,
              href: ensureLeadingTrailingSlash(`${siteURL}${lang.url}`),
            },
          })
        )
      }
    })

    /**
     * Exclude pages from the sitemap:
     *  - Glossary directory
     *  - All UK glossary entries
     */
    sitemapObj.urlset.url = sitemapObj.urlset.url.filter((page) => {
      const loc = page.loc[0]

      if (
        loc === `${siteURL}uk/glossary/` ||
        loc === `${siteURL}eu/glossary/` ||
        loc.includes(`${siteURL}uk/glossary/`)
      ) {
        return false
      }

      return true
    })

    const builder = new Builder({
      xmldec: {
        version: "1.0",
        encoding: "UTF-8",
        standalone: null,
      },
      renderOpts: { pretty: true },
    })

    const updatedSitemapXML = builder.buildObject(sitemapObj)

    fs.writeFileSync(sitemapPath, updatedSitemapXML)
    reporter.info("Sitemap updated with alternate links")
  } catch (error) {
    reporter.panic("Failed to update sitemap", error)
  }
}

exports.onPostBuild = async ({ graphql, reporter }) => {
  reporter.info("Gatsby build completed")

  _analyzeBuildSize(reporter)

  await updateSitemapWithAlternateLinks(graphql, reporter)

  const sitemapPath = "./public/sitemap-0.xml"
  const stockSitemapPath = "./public/sitemap-stocks.xml"
  const etfSitemapPath = "./public/sitemap-etfs.xml"
  const sitemapIndexPath = "./public/sitemap-index.xml"

  try {
    // Read the original sitemap
    const originalSitemapXML = fs.readFileSync(sitemapPath, "utf8")

    // Copy the original sitemap to create a stock sitemap
    fs.writeFileSync(stockSitemapPath, originalSitemapXML)
    fs.writeFileSync(etfSitemapPath, originalSitemapXML)

    // Parse both sitemaps
    const originalSitemapObj = await parseStringPromise(originalSitemapXML)
    const stockSitemapObj = await parseStringPromise(originalSitemapXML)
    const etfSitemapObj = await parseStringPromise(originalSitemapXML)

    // Filter the URLs in the original sitemap to remove stock and etf entries
    originalSitemapObj.urlset.url = originalSitemapObj.urlset.url.filter(
      (page) =>
        !(
          page.loc[0].includes(`/${LanguageEnum.English}/stocks/`) ||
          page.loc[0].includes(`/${LanguageEnum.Greek}/stocks/`) ||
          page.loc[0].includes(`/${LanguageEnum.English}/etfs/`) ||
          page.loc[0].includes(`/${LanguageEnum.Greek}/etfs/`)
        )
    )

    // Filter the URLs in the stock sitemap to keep only stock entries
    stockSitemapObj.urlset.url = stockSitemapObj.urlset.url.filter((page) =>
      page.loc[0].includes(`/${LanguageEnum.English}/stocks/`)
    )

    // Filter the URLs in the etfs sitemap to keep only etf entries
    etfSitemapObj.urlset.url = etfSitemapObj.urlset.url.filter((page) =>
      page.loc[0].includes(`/${LanguageEnum.English}/etfs/`)
    )

    const builder = new Builder({
      xmldec: {
        version: "1.0",
        encoding: "UTF-8",
        standalone: null,
      },
      renderOpts: { pretty: true },
    })

    // Build and write the updated sitemaps
    const updatedOriginalSitemapXML = builder.buildObject(originalSitemapObj)
    fs.writeFileSync(sitemapPath, updatedOriginalSitemapXML)

    const updatedStockSitePathXML = builder.buildObject(stockSitemapObj)
    fs.writeFileSync(stockSitemapPath, updatedStockSitePathXML)

    const updatedEtfSitemapXML = builder.buildObject(etfSitemapObj)
    fs.writeFileSync(etfSitemapPath, updatedEtfSitemapXML)

    // Update the sitemap index

    if (!fs.existsSync(sitemapIndexPath)) {
      reporter.panic(
        "Failed to find index sitemap",
        new Error("No index sitemap found")
      )
    }

    const sitemapIndexXML = fs.readFileSync(sitemapIndexPath, "utf8")
    const sitemapIndexObj = await parseStringPromise(sitemapIndexXML)

    const sitemapIndexEntries = [
      { loc: `${siteURL}sitemap-0.xml` },
      { loc: `${siteURL}sitemap-stocks.xml` },
      { loc: `${siteURL}sitemap-etfs.xml` },
    ]

    sitemapIndexObj.sitemapindex.sitemap = sitemapIndexEntries.map((entry) => ({
      loc: [entry.loc],
    }))

    const updatedSitemapIndexXML = builder.buildObject(sitemapIndexObj)
    fs.writeFileSync(sitemapIndexPath, updatedSitemapIndexXML)

    reporter.info("Sitemaps updated and sitemap index updated")
  } catch (error) {
    reporter.panic("Failed to update sitemaps", error)
  }
}

const createFinancialReportsPages = async (actions, reporter) => {
  reporter.verbose("Creating Financial Reports pages...")
  const { createPage } = actions

  const financialReportsTemplate = path.resolve(
    `./src/templates/financial-reports-page.js`
  )

  SUPPORTED_LANGUAGE_REGION_PAIRS.forEach((languageRegionPair) => {
    const [language, regionEnumString] = languageRegionPair.split("-")
    const region = regionEnumString.toLowerCase()

    const lowerCaseLanguageRegion = languageRegionPair.toLowerCase()
    const pathname = ensureLeadingTrailingSlash(
      `/${lowerCaseLanguageRegion}/financial-statements/`
    )
    reporter.info(
      `Creating page: ${pathname} for langRegion ${lowerCaseLanguageRegion}`
    )

    createPage({
      path: pathname,
      component: financialReportsTemplate,
      context: {
        language,
        region: region,
        languageRegion: lowerCaseLanguageRegion,
        pathname,
      },
    })
  })
  reporter.verbose("Finished creating Financial Reports pages.")
}

const createLearnDirectory = async (actions, reporter) => {
  reporter.verbose("Creating learning guides directory pages...")
  const { createPage } = actions

  // For locales (UK and EU)
  for (const locale of [LocaleEnum.EU, LocaleEnum.UK]) {
    // Map locale to default language-region for that locale
    // This ensures translation works for locale-based routes
    let defaultLanguageRegion
    if (locale === LocaleEnum.UK) {
      defaultLanguageRegion = "en-gb" // English for UK
    } else if (locale === LocaleEnum.EU) {
      defaultLanguageRegion = "en-gr" // English for EU/Greece
    }
    const pathname = ensureLeadingTrailingSlash(`/${locale}/learning-guides/`)
    createPage({
      path: pathname,
      component: path.resolve(
        `./src/templates/learning-guides-directory-template.js`
      ),
      context: {
        locale,
        languageRegion: defaultLanguageRegion,
        pathname,
      },
    })
  }

  // For each supported language-region pair
  SUPPORTED_LANGUAGE_REGION_PAIRS.forEach((languageRegion) => {
    const [language, region] = languageRegion.split("-")
    const locale = REGION_TO_DEFAULT_LOCALE[region.toLowerCase()]
    const pathname = ensureLeadingTrailingSlash(
      `/${languageRegion.toLowerCase()}/learning-guides/`
    )
    createPage({
      path: pathname,
      component: path.resolve(
        `./src/templates/learning-guides-directory-template.js`
      ),
      context: {
        languageRegion,
        locale,
        pathname,
      },
    })
  })
}

const createPagesFromMarkdown = async (graphql, actions, reporter) => {
  const result = await graphql(`
    query {
      pages: allMarkdownRemark(
        filter: { fileAbsolutePath: { glob: "**/content/pages/*.md" } }
      ) {
        edges {
          node {
            fields {
              slug
            }
            frontmatter {
              path
              title
            }
          }
        }
        nodes {
          frontmatter {
            title
          }
          html
        }
      }
    }
  `)

  // Handle errors
  if (result.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const { createPage } = actions

  // Create other markdown pages
  const allMarkdownPages = result.data.pages.edges

  allMarkdownPages.forEach(({ node }) => {
    const pathname = ensureLeadingTrailingSlash(node.frontmatter.path)
    createPage({
      path: pathname,
      component: path.resolve(`./src/templates/markdown-page-template.js`),
      context: {
        title: node.frontmatter.title,
        pathname,
      },
    })
  })
}

const createPressReleaseDirectoryPages = async (graphql, actions, reporter) => {
  reporter.verbose("Creating Press Release Directory pages...")
  const result = await graphql(`
    query {
      pressReleases: allMarkdownRemark(
        filter: { fileAbsolutePath: { glob: "**/content/press-releases/*.md" } }
      ) {
        nodes {
          fileAbsolutePath # Ensure we fetch the file path
          frontmatter {
            path
            title
            subtitle
            longSubtitle
            heroImage {
              childImageSharp {
                gatsbyImageData(
                  width: 336
                  quality: 100
                  layout: FIXED
                  placeholder: BLURRED
                  formats: [AUTO, WEBP]
                )
              }
            }
          }
          html # Fetch the rendered HTML
        }
      }
    }
  `)

  // Handle errors
  if (result.errors) {
    reporter.panicOnBuild(
      `Error while running GraphQL query for press releases. ${result.errors}`
    )
    return
  }

  const { createPage } = actions
  const allPressReleasePages = result.data.pressReleases.nodes

  if (!allPressReleasePages || allPressReleasePages.length === 0) {
    reporter.info("No press release markdown files found. Skipping creation.")
    return
  }

  const pressReleasesTemplate = path.resolve(
    `./src/templates/press-releases-directory-page-template.js`
  )

  const languages = Object.values(LanguageEnum)
  const languageToLanguageRegionMap = {}
  Object.entries(REGION_TO_DEFAULT_LANGUAGE).forEach(([region, language]) => {
    languageToLanguageRegionMap[language] = `${language}-${region}`
  })

  for (const language of languages) {
    const languageRegion = languageToLanguageRegionMap[language]
    const pathname = ensureLeadingTrailingSlash(`/${language}/press-releases`)
    reporter.info(`Creating page: ${pathname} for langRegion ${languageRegion}`)

    createPage({
      path: pathname,
      component: pressReleasesTemplate,
      context: {
        pressReleases: allPressReleasePages
          .filter((pressRelease) => {
            const filename = pressRelease.fileAbsolutePath.substring(
              pressRelease.fileAbsolutePath.lastIndexOf("/") + 1
            )
            return filename.split("-", 1)[0] === language
          })
          .map((pressRelease) => ({
            title: pressRelease.frontmatter.title,
            subtitle: pressRelease.frontmatter.subtitle,
            longSubtitle: pressRelease.frontmatter.longSubtitle,
            path: pressRelease.frontmatter.path,
            heroImage: pressRelease.frontmatter.heroImage,
          })),
        language,
        languageRegion,
        pathname,
      },
    })
  }

  reporter.verbose("Finished creating Press Release Directory pages.")
}

const createPressReleasePages = async (graphql, actions, reporter) => {
  reporter.verbose("Creating press release pages from Markdown...")
  const result = await graphql(`
    query {
      pressReleases: allMarkdownRemark(
        filter: { fileAbsolutePath: { glob: "**/content/press-releases/*.md" } }
      ) {
        nodes {
          fileAbsolutePath # Ensure we fetch the file path
          frontmatter {
            path
            title
            subtitle
            heroImage {
              childImageSharp {
                gatsbyImageData(
                  width: 1200
                  quality: 90
                  placeholder: BLURRED
                  formats: [AUTO, WEBP]
                )
              }
            }
          }
          html # Fetch the rendered HTML
        }
      }
    }
  `)

  // Handle errors
  if (result.errors) {
    reporter.panicOnBuild(
      `Error while running GraphQL query for press releases. ${result.errors}`
    )
    return
  }

  const { createPage } = actions
  const allPressReleasePages = result.data.pressReleases.nodes

  if (!allPressReleasePages || allPressReleasePages.length === 0) {
    reporter.info("No press release markdown files found. Skipping creation.")
    return
  }

  // Map language to default language-region (similar to glossary)
  const languageToDefaultLanguageRegionMap = {}
  Object.entries(REGION_TO_DEFAULT_LANGUAGE).forEach(([region, language]) => {
    // Ensure we don't overwrite if a language maps to multiple default regions (use first encountered)
    if (!languageToDefaultLanguageRegionMap[language]) {
      languageToDefaultLanguageRegionMap[language] = `${language}-${region}`
    }
  })

  // For each press release markdown file
  allPressReleasePages.forEach((node) => {
    // Basic check if path exists in frontmatter (Keep existing check)
    if (!node.frontmatter || !node.frontmatter.path) {
      reporter.warn(
        `Skipping press release page creation for node without a path in frontmatter.`
      )
      return
    }

    // Extract language prefix from filename (Keep existing logic)
    const filename = node.fileAbsolutePath.split("/").pop()
    const fileLangPrefix = filename.split("-")[0].toLowerCase() // e.g., 'en' or 'el'

    if (!fileLangPrefix) {
      reporter.warn(
        `Could not extract language prefix from filename: ${filename}. Skipping page creation for this file.`
      )
      return
    }

    const basePath = node.frontmatter.path
    const title = node.frontmatter.title || "Press Release"
    const subtitle = node.frontmatter.subtitle || ""

    // Check if the file's language prefix is a supported language
    if (
      Object.values(LanguageEnum)
        .map((l) => l.toLowerCase())
        .includes(fileLangPrefix)
    ) {
      const language = fileLangPrefix // Use the file's language prefix directly

      // Determine the default languageRegion and locale for context
      const defaultLanguageRegion = languageToDefaultLanguageRegionMap[language]
      if (!defaultLanguageRegion) {
        reporter.warn(
          `Could not determine default languageRegion for language: ${language} from file ${filename}. Skipping page creation.`
        )
        return
      }
      const [_, region] = defaultLanguageRegion.split("-")
      const locale = REGION_TO_DEFAULT_LOCALE[region.toLowerCase()]

      // Create the language-based pathname
      const pathname = ensureLeadingTrailingSlash(
        `/${language.toLowerCase()}${basePath}`.toLowerCase()
      )

      createPage({
        path: pathname,
        component: path.resolve(
          `./src/templates/press-release-page-template.js`
        ),
        context: {
          title: title,
          html: node.html,
          pathname,
          language, // Pass the language
          locale, // Pass the derived locale
          languageRegion: defaultLanguageRegion, // Pass the default languageRegion for context consistency
          heroImage: node.frontmatter.heroImage,
          subtitle,
        },
      })
      reporter.info(
        `Created press release page: ${pathname} for language ${language} from ${filename}`
      )
    } else {
      reporter.warn(
        `Language prefix '${fileLangPrefix}' from filename ${filename} is not a supported language. Skipping page creation.`
      )
    }
  })

  reporter.verbose(`Finished creating press release pages based on language.`)
}

const createBlogPages = async (graphql, actions, reporter) => {
  const blogPostsResults = await graphql(`
    {
      allContentfulPostsBlog {
        nodes {
          slug
          metaTitle
          metaDescription
          metaImage {
            file {
              url
            }
          }
          title
          image {
            file {
              url
            }
          }
          createdAt
          date
          wordCount
          locale {
            code
          }
          schemaFaqs {
            answer
            question
          }
          hrefLangs {
            url
            languageCode
          }
          schemaSteps {
            title
            steps {
              answer
              question
            }
          }
          author {
            name
            slug
            shortDescription
            linkedin
            twitter
            medium
            reddit
            locale {
              code
            }
            mail
            image {
              file {
                url
              }
            }
          }
          tags {
            name
          }
          category {
            slug
            name
            locale {
              code
            }
            color
          }
          content {
            raw
            references {
              ... on ContentfulButtonBlog {
                href
                label
                textColor
                backgroundColor
                contentful_id
              }
              ... on ContentfulAsset {
                url
                contentful_id
                title
                filename
              }
            }
          }
          relevantPosts {
            slug
            title
            metaDescription
            readingTime
            image {
              file {
                url
              }
            }
            category {
              name
              color
              slug
            }
            author {
              name
              slug
              image {
                file {
                  url
                }
              }
            }
            date
            updatedAt
            createdAt
          }
        }
      }
    }
  `)

  if (blogPostsResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const { createPage } = actions

  blogPostsResults.data.allContentfulPostsBlog.nodes.forEach((post) => {
    const locale = post.locale?.code
    if (!Object.values(LocaleEnum).includes(locale)) {
      reporter.panicOnBuild(
        `Invalid locale for blog post ${post.slug}. Locale value: ${locale}.`
      )
      return
    }
    //  check that category has the same locale as well. It's a safety measure so we do not break our urls from any mistake on the cms.
    if (locale !== post.category.locale.code) {
      reporter.panicOnBuild(
        `Post locale and category locale should be the same for post ${post.name}. 
         post locale: ${locale}
         category locale: ${post.category.locale.code}
        `
      )
      return
    }
    if (locale !== post.author.locale.code) {
      reporter.panicOnBuild(
        `Post locale and author locale should be the same for post ${post.slug}.
         post locale: ${locale}
         author locale: ${post.author.locale.code}
        `
      )
      return
    }
    const pathname = ensureLeadingTrailingSlash(
      `/${locale}/blog/${post.category.slug}/${post.slug}`.toLowerCase()
    )
    createPage({
      path: pathname,
      component: path.resolve(`./src/templates/blog-post-template.js`),
      context: {
        ...post,
        locale,
        pathname,
      },
    })
  })
}

const createAuthorPages = async (graphql, actions, reporter) => {
  // Create blog Authors pages.
  const blogAuthorsResults = await graphql(`
    {
      allContentfulAuthorsBlog {
        nodes {
          id
          slug
          name
          shortDescription
          linkedin
          twitter
          medium
          reddit
          mail
          image {
            file {
              url
            }
          }
          description {
            raw
          }
          jobTitle
          schemaSocials {
            url
          }
          schemaKnowsAbout {
            name
            sameAs
            contentfulid
          }
          locale {
            code
          }
          hrefLangs {
            url
            languageCode
          }
          metaDescription
          metaTitle
          metaImage {
            file {
              url
            }
          }
        }
      }
    }
  `)

  if (blogAuthorsResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }
  const { createPage } = actions

  // Create blog author post pages.
  blogAuthorsResults.data.allContentfulAuthorsBlog.nodes.forEach((author) => {
    const locale = author.locale?.code

    if (!Object.values(LocaleEnum).includes(locale)) {
      reporter.panicOnBuild(
        `Invalid locale for author ${author.name}. Locale value: ${locale}.`
      )
      return
    }
    const pathname = ensureLeadingTrailingSlash(
      `${locale}/blog/${author.slug}`.toLowerCase()
    )
    createPage({
      path: pathname,
      component: path.resolve(`./src/templates/blog-author-template.js`),
      context: {
        ...author,
        authorId: author.id,
        locale,
        pathname,
      },
    })
  })
}

const createCategoryPages = async (graphql, actions, reporter) => {
  // category page

  const blogCategoriesResults = await graphql(`
    {
      allContentfulCategoriesBlog {
        nodes {
          id
          name
          slug
          title
          color
          metaDescription
          metaTitle
          hrefLangs {
            url
            languageCode
          }
          locale {
            code
          }
          metaImage {
            file {
              url
            }
          }
        }
      }
    }
  `)

  if (blogCategoriesResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const { createPage } = actions

  // Create category pages.
  blogCategoriesResults.data.allContentfulCategoriesBlog.nodes.forEach(
    (category) => {
      const locale = category.locale?.code
      if (!Object.values(LocaleEnum).includes(locale)) {
        reporter.panicOnBuild(
          `Invalid locale for category ${category.name}. Locale value: ${locale}.`
        )
        return
      }
      const pathname = ensureLeadingTrailingSlash(
        `${locale}/blog/${category.slug}`.toLowerCase()
      )
      createPage({
        path: pathname,
        component: path.resolve(`./src/templates/blog-category-template.js`),
        context: {
          ...category,
          categoryId: category.id,
          locale,
          pathname,
        },
      })
    }
  )
}

const createAssetPages = async (graphql, actions, reporter) => {
  reporter.verbose("Creating asset pages...")

  const { createPage } = actions

  await RedisService.Instance.scanKeysPaginated(
    "public_asset_data:stock*",
    (data) => {
      const assetCommonId = data.key

      if (
        isDevelopment &&
        !["equities_amazon", "equities_apple", "equities_microsoft"].includes(
          assetCommonId
        )
      ) {
        return
      }

      const ticker = data.about.ticker
      const exchange = data.about.tradingOn

      // Iterate over supported languages for asset pages
      ;[LanguageEnum.English].forEach((language) => {
        const pathname = ensureLeadingTrailingSlash(
          `${language}/stocks/${ticker.toLowerCase()}.${exchange.toLowerCase()}`
        )
        createPage({
          path: pathname,
          component: path.resolve(
            "./src/templates/asset-page-stock-template.js"
          ),
          context: {
            locale: LocaleEnum.EU, // Keep locale for internal use
            stockData: data,
            formatLocale: "en",
            language, // Add language to context
            ticker,
            exchange,
            assetCommonId: assetCommonId,
            pathname,
          },
        })
      })
    }
  )

  await RedisService.Instance.scanKeysPaginated(
    "public_asset_data:etf*",
    (data) => {
      const assetCommonId = data.key

      if (
        isDevelopment &&
        !["equities_us", "equities_global"].includes(assetCommonId)
      ) {
        return
      }

      const ticker = data.about.ticker
      const exchange = data.about.tradingOn

      // Iterate over supported languages for asset pages
      ;[LanguageEnum.English].forEach((language) => {
        const pathname = ensureLeadingTrailingSlash(
          `${language}/etfs/${ticker.toLowerCase()}.${exchange.toLowerCase()}`
        )
        createPage({
          path: pathname,
          component: path.resolve("./src/templates/asset-page-etf-template.js"),
          context: {
            locale: LocaleEnum.EU, // Keep locale for internal use
            etfData: data,
            formatLocale: "en",
            language, // Add language to context
            ticker,
            exchange,
            assetCommonId,
            pathname,
          },
        })
      })
    }
  )
}

const createLegalPages = async (graphql, actions, reporter) => {
  const legalPagesResults = await graphql(`
    {
      allContentfulLegalPage {
        nodes {
          slug
          metaDescription
          metaTitle
          title
          lastUpdated
          locale {
            code
          }
          contentPart1 {
            contentPart1
          }
          contentPart2 {
            contentPart2
          }
        }
      }
    }
  `)

  if (legalPagesResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const { createPage } = actions

  legalPagesResults.data.allContentfulLegalPage.nodes.forEach((legalPage) => {
    const locale = legalPage.locale?.code
    if (!Object.values(LocaleEnum).includes(locale)) {
      reporter.panicOnBuild(
        `Invalid locale for legal page ${legalPage.slug}. Locale value: ${locale}.`
      )
      return
    }

    const content = `${legalPage?.contentPart1?.contentPart1 ?? ""}\n${legalPage?.contentPart2?.contentPart2 ?? ""}`
    const pathname = ensureLeadingTrailingSlash(
      `${locale}/${legalPage.slug}`.toLowerCase()
    )
    createPage({
      path: pathname,
      component: path.resolve(`./src/templates/legal-page-template.js`),
      context: {
        ...legalPage,
        locale,
        content,
        pathname,
      },
    })
  })
}

const createHelpCentrePages = async (graphql, actions, reporter) => {
  const helpCentreResults = await graphql(`
    {
      allContentfulFaqCategory(filter: { isHelpCentreCategory: { eq: true } }) {
        nodes {
          wealthyhoodId
          order
          slug
          metaTitle
          metaDescription
          title
          subtitle
          locale {
            code
          }
          region
          language
          faqs {
            answer {
              answer
            }
            question
          }
        }
      }
    }
  `)

  if (helpCentreResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const { createPage } = actions

  // Create help center pages for each locale (UK and EU)
  for (const localeCode of [LocaleEnum.EU, LocaleEnum.UK]) {
    const helpCentreCategories =
      helpCentreResults.data.allContentfulFaqCategory.nodes
        // for translated content, locale can be undefined
        .filter(({ locale }) => locale?.code === localeCode)
        .sort((a, b) => a.order - b.order)

    if (!helpCentreCategories.length) {
      reporter.panicOnBuild(
        `No help-centre categories found for ${localeCode} locale.`
      )
      return
    }

    const mainPathname = ensureLeadingTrailingSlash(
      `/${localeCode}/help-centre`.toLowerCase()
    )
    createPage({
      path: mainPathname,
      component: path.resolve(`./src/templates/help-centre.js`),
      context: {
        categories: helpCentreCategories,
        locale: localeCode,
        pathname: mainPathname,
      },
    })

    helpCentreCategories.forEach((category) => {
      const { locale: _, ...categoryWithoutLocale } = category
      const categoryPathname = ensureLeadingTrailingSlash(
        `/${localeCode}/help-centre/${category.slug}`.toLowerCase()
      )
      createPage({
        path: categoryPathname,
        component: path.resolve(`./src/templates/help-centre-category.js`),
        context: {
          ...categoryWithoutLocale,
          locale: localeCode,
          pathname: categoryPathname,
        },
      })
    })
  }

  // Create help center pages for each supported language-region pair
  SUPPORTED_LANGUAGE_REGION_PAIRS.forEach((languageRegion) => {
    const [language, region] = languageRegion.split("-")

    // First try to find categories by language and region
    let helpCentreCategories =
      helpCentreResults.data.allContentfulFaqCategory.nodes
        .filter(
          (category) =>
            category.language === language &&
            category.region?.toString().toLowerCase() === region.toLowerCase()
        )
        .sort((a, b) => a.order - b.order)

    // If no categories found, fall back to checking by locale (e.g. en-gr -> eu)
    if (!helpCentreCategories.length) {
      const localeCode = REGION_TO_DEFAULT_LOCALE[region.toLowerCase()]
      reporter.info(
        `No direct language-region match for ${languageRegion}, trying locale fallback with code: ${localeCode}`
      )
      helpCentreCategories =
        helpCentreResults.data.allContentfulFaqCategory.nodes
          // for translated content, locale can be undefined
          .filter(({ locale }) => locale?.code === localeCode)
          .sort((a, b) => a.order - b.order)
    }

    if (!helpCentreCategories.length) {
      reporter.info(
        `No help-centre categories found for ${languageRegion} language-region pair.`
      )
      return
    }

    // Create main help centre page
    const mainPathname = ensureLeadingTrailingSlash(
      `/${languageRegion.toLowerCase()}/help-centre/`
    )
    createPage({
      path: mainPathname,
      component: path.resolve(`./src/templates/help-centre.js`),
      context: {
        categories: helpCentreCategories,
        languageRegion,
        pathname: mainPathname,
      },
    })

    // Create category pages
    helpCentreCategories.forEach((category) => {
      const { locale: _, ...categoryWithoutLocale } = category
      const categoryPathname = ensureLeadingTrailingSlash(
        `/${languageRegion.toLowerCase()}/help-centre/${category.slug}/`
      )
      createPage({
        path: categoryPathname,
        component: path.resolve(`./src/templates/help-centre-category.js`),
        context: {
          ...categoryWithoutLocale,
          languageRegion,
          pathname: categoryPathname,
        },
      })
    })
  })
}

function parseAndSortGlossary(entries) {
  // Sort the entries alphabetically by the title field
  const sortedEntries = [...entries].sort((a, b) =>
    a.title.localeCompare(b.title)
  )

  // Create an array pairing indexes with entries
  const glossaryDictionary = sortedEntries.reduce(
    (glossaryDictionary, entry) => {
      if (glossaryDictionary[entry.index]) {
        glossaryDictionary[entry.index].push(entry)
      } else {
        glossaryDictionary[entry.index] = [entry]
      }

      return glossaryDictionary
    },
    {}
  )

  return Object.entries(glossaryDictionary)
    .map(([glossaryIndex, entries]) => {
      return {
        index: glossaryIndex,
        entries,
      }
    })
    .sort((a, b) => a.index.localeCompare(b.index))
}

const createGlossaryPages = async (graphql, actions, reporter) => {
  const glossaryPageResults = await graphql(`
    {
      allContentfulGlossary {
        nodes {
          title
          definition {
            raw
          }
        }
      }
    }
  `)

  if (glossaryPageResults.errors) {
    reporter.panicOnBuild(`Error while running GraphQL query.`)
    return
  }

  const { createPage } = actions

  const glossaryEntries = glossaryPageResults.data.allContentfulGlossary.nodes
    .map((contentfulEntry) => ({
      index: contentfulEntry.title?.[0],
      title: contentfulEntry.title,
      answer: contentfulEntry.definition?.raw,
    }))
    // Workaround for unpublished glossary entries in dev/sandbox environment
    .filter((contentfulEntry) => !!contentfulEntry.index)

  const languages = Object.values(LanguageEnum)
  const languageToLanguageRegionMap = {}
  Object.entries(REGION_TO_DEFAULT_LANGUAGE).forEach(([region, language]) => {
    languageToLanguageRegionMap[language] = `${language}-${region}`
  })

  // Parse and organize glossary data
  const parsedGlossary = parseAndSortGlossary(glossaryEntries)

  // Create a lightweight version of glossary data containing only what's needed for navigation
  const glossaryNavigation = parsedGlossary.map((category) => ({
    index: category.index,
    firstEntryTitle: category.entries[0].title,
  }))

  for (const language of languages) {
    const languageRegion = languageToLanguageRegionMap[language]
    const directoryPathname = ensureLeadingTrailingSlash(
      `/${language}/glossary`
    )

    createPage({
      path: directoryPathname,
      component: path.resolve(`./src/templates/glossary-directory-template.js`),
      context: {
        glossary: parseAndSortGlossary(glossaryEntries),
        language,
        languageRegion,
        pathname: directoryPathname,
      },
    })

    for (const glossaryEntry of glossaryEntries) {
      // Find only entries for the current letter
      const currentLetterCategory = parsedGlossary.find(
        (category) => category.index === glossaryEntry.index
      )

      // For entry pages, only pass the entries from the same letter and the navigation data
      const entryPathname = ensureLeadingTrailingSlash(
        `${language}/glossary/${glossaryEntry.index}/${slugify(glossaryEntry.title)}`.toLowerCase()
      )
      createPage({
        path: entryPathname,
        component: path.resolve(`./src/templates/glossary-entry-template.js`),
        context: {
          glossaryNavigation: glossaryNavigation,
          currentLetterEntries: currentLetterCategory.entries,
          selectedEntry: glossaryEntry,
          language,
          languageRegion,
          activeIndex: glossaryEntry.index,
          pathname: entryPathname,
        },
      })
    }
  }
}

const createMainPages = async (graphql, actions, reporter) => {
  reporter.verbose("Creating main pages for each language-region pair...")

  const { createPage } = actions

  // Create index and pricing pages for each supported language-region pair
  SUPPORTED_LANGUAGE_REGION_PAIRS.forEach((languageRegion) => {
    const [language, region] = languageRegion.split("-")
    const locale = REGION_TO_DEFAULT_LOCALE[region.toLowerCase()]

    const indexPathname = ensureLeadingTrailingSlash(
      `/${languageRegion.toLowerCase()}/`
    )
    // Create index page
    createPage({
      path: indexPathname,
      component: path.resolve(`./src/templates/index-page-template.js`),
      context: {
        languageRegion,
        pathname: indexPathname,
        // This will be used for GraphQL queries
        language,
        region: region.toLowerCase(),
      },
    })

    const pricingPathname = ensureLeadingTrailingSlash(
      `/${languageRegion.toLowerCase()}/pricing/`
    )
    // Create pricing page
    createPage({
      path: pricingPathname,
      component: path.resolve(`./src/templates/pricing-page-template.js`),
      context: {
        languageRegion,
        locale,
        pathname: pricingPathname,
      },
    })

    const referralsPathname = ensureLeadingTrailingSlash(
      `/${languageRegion.toLowerCase()}/referrals/`
    )
    // Create referrals page
    createPage({
      path: referralsPathname,
      component: path.resolve(`./src/templates/referrals-page-template.js`),
      context: {
        languageRegion,
        pathname: referralsPathname,
        // This will be used for GraphQL queries
        language,
        region: region.toLowerCase(),
      },
    })
  })
}

const _analyzeBuildSize = (reporter) => {
  // Report sizes of build directories
  reporter.info("Analyzing build size:")
  try {
    const publicDirSize = execSync("du -sh ./public").toString()
    reporter.info(`Total public directory size: ${publicDirSize}`)

    // Get sizes of subdirectories, sorted by size (largest first)
    reporter.info("Subdirectory sizes (sorted by size):")
    const subdirSizes = execSync("du -sh ./public/* | sort -rh").toString()
    reporter.info(subdirSizes)
  } catch (error) {
    reporter.warn("Could not analyze build directory sizes: " + error.message)
  }
}
