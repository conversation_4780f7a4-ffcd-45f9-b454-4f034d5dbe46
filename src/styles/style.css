@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  @media (max-width: 640px) {
    .tw-hide-scrollbar-in-small-screens {
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    .tw-hide-scrollbar-in-small-screens::-webkit-scrollbar {
      display: none; /* WebKit browsers */
    }
  }
}

:root {
  --animation-duration: 5s; /* Duration in seconds */
  --animation-duration-tabs: 10s; /* Duration in seconds */
}

@font-face {
  font-family: "Noto Sans";
  src: url("../fonts/NotoSans.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: "Noto Sans Display";
  src: url("../fonts/NotoSansDisplay.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
}

h1 {
  font-family: "Work Sans", Roboto, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 6rem;
  line-height: 100%;
}

h2 {
  font-family: "Work Sans", Roboto, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 3.5rem;
  line-height: 120%;
}

h3 {
  font-family: "Work Sans", Roboto, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 120%;
}

h4 {
  font-family: "Work Sans", Roboto, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 2rem;
  line-height: 130%;
}

h5 {
  font-family: "Work Sans", Roboto, sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 120%;
}

/* Press Release Page Styles */
.press-release-page h1 {
  font-size: 3.5rem;
  margin-top: 4.5rem;
  margin-bottom: 3rem;
}

.press-release-page h2 {
  font-size: 2.5rem;
  margin-top: 4rem;
  margin-bottom: 1.5rem;
}

.press-release-page h3 {
  font-size: 2rem;
  margin-top: 3.75rem;
  margin-bottom: 1.25rem;
}

.press-release-page hr {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.press-release-page ul {
  list-style-type: disc !important;
  padding-left: 2rem !important;
  margin-bottom: 1.5rem;
}

.press-release-page li {
  margin-bottom: 0.5rem !important;
  display: list-item !important;
}

.press-release-page li strong {
  font-weight: 600;
}

/* Press Release content overrides for legal-content list styling */
.press-release-content.legal-content > ul {
  list-style-type: disc !important;
  padding-left: 2rem !important;
  margin-top: 1em !important;
  margin-bottom: 1.5em !important;
}

.press-release-content.legal-content > ul > li {
  display: list-item !important;
  margin-bottom: 0.5rem !important;
}

.press-release-content.legal-content p {
  margin-bottom: 1.5rem;
}

@media (max-width: 576px) {
  .press-release-page h1 {
    font-size: 2.2rem !important;
  }
  
  .press-release-page h2 {
    font-size: 1.8rem !important;
  }
  
  .press-release-page h3 {
    font-size: 1.3rem !important;
  }
}

.text-big {
  font-family: "Poppins", Roboto, sans-serif;
  font-style: normal;
  font-weight: 400;
  /* 24px */
  font-size: 1.5rem;
  line-height: 150%;
}

.text-big-no-font-family {
  font-style: normal;
  font-weight: 400;
  /* 24px */
  font-size: 1.5rem;
  line-height: 150%;
}

.text-medium {
  font-family: "Poppins", Roboto, sans-serif;
  font-style: normal;
  font-weight: 400;
  /* 18px */
  font-size: 1.125rem;
  line-height: 140%;
}

.text-medium-no-font-family {
  font-style: normal;
  font-weight: 400;
  /* 18px */
  font-size: 1.125rem;
  line-height: 140%;
}

.text-regular {
  font-family: "Poppins", Roboto, sans-serif;
  font-style: normal;
  font-weight: 400;
  /* 16px */
  font-size: 1rem;
  line-height: 140%;
}
.text-regular-no-font-family {
  font-style: normal;
  font-weight: 400;
  /* 16px */
  font-size: 1rem;
  line-height: 140%;
}

.text-small {
  font-family: "Poppins", Roboto, sans-serif !important;
  font-style: normal !important;
  font-weight: 400 !important;
  /* 12px */
  font-size: 0.75rem !important;
  line-height: 150% !important;
}

.navbar-nav-item {
  padding-right: 0.75rem;
}

.navbar-nav-item:hover .nav-link,
.navbar-nav-item:focus .nav-link {
  color: #546be5 !important;
}

.mobile-navbar {
  padding-top: 5.5rem !important;
  padding-right: 1rem !important;
  padding-bottom: 1.5rem !important;
  padding-left: 1rem !important;
}

.mobile-navbar .nav-link {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.mobile-navbar a {
  padding-left: 1.5rem !important;
}

.mobile-navbar .selected {
  background-color: rgba(84, 107, 229, 0.08);
  border-radius: 32px;
}

.main-bg-color {
  background-color: #14233c;
}

.cta-input-button,
.cta-button {
  border: none !important;
  border-radius: 32px !important;
  width: fit-content !important;
}

.cta-input-button.bg-dark-blue:hover,
.cta-button.bg-dark-blue:hover {
  background-color: #232c5c !important;
  box-shadow: 6px 12px 20px rgba(27, 39, 100, 0.24);
}

.cta-input-button.bg-light-blue:hover,
.cta-button.bg-light-blue:hover {
  background-color: #6a82fe !important;
  box-shadow: 6px 12px 20px rgba(84, 107, 229, 0.24);
}

.cta-input-button span,
.cta-button span {
  height: 100%;
}

.cta-input-button svg,
.cta-button svg {
  height: 100%;
}

.cta-input-button p,
.cta-button p {
  height: 100%;
  margin-top: 0;
  margin-bottom: 0;
  line-height: 26px !important;
}

.button-tall {
  height: 61px !important;
  padding: 1.125rem 2rem;
}

.button-medium {
  height: 53px !important;
  padding: 0.875rem 2rem;
}

.button-short {
  height: 45px !important;
  padding: 0.625rem 2rem;
}

.email-cta-button {
  height: 45px !important;
  padding: 0.5rem 1.5rem;
}

.button-slim {
  height: 32px !important;
  padding: 0 0.85rem;
}

.bg-light-grey {
  background-color: #f7f7f7 !important;
}

.text-light-grey {
  color: #101327 !important;
  opacity: 0.6 !important;
}

.faq-content p,
.faq-content li,
.faq-content ul,
.faq-content span {
  opacity: 0.9 !important;
}

.faq-content ul,
.faq-content ol {
  list-style: auto !important;
  padding-left: revert !important;
  margin-bottom: 0.75rem;
}

.faq-content li {
  margin-bottom: 0.5rem;
}

.faq-content p {
  margin-bottom: 1rem;
}

.faq-content strong {
  color: black;
  font-weight: 500;
}

.faq-content a {
  text-decoration: none;
  color: #546be5;
  font-weight: 500;
}

.text-grey {
  color: #70717d !important;
}

.text-white {
  color: #fff !important;
  opacity: 1 !important;
}

.text-pink {
  color: #ffc6d7 !important;
}

.text-dark-pink {
  color: #e3658b !important;
}

.bg-background-white {
  background-color: #f6f8fd !important;
}

.bg-dark-blue {
  background-color: #101327 !important;
}

.text-dark-blue {
  color: #101327 !important;
}

.bg-medium-blue {
  background-color: #171d3f !important;
}

.bg-light-blue {
  background-color: #546be5 !important;
}

.text-light-blue {
  color: #546be5 !important;
}

div.selected-faq-card {
  background-color: #546be5 !important;
}

div.not-selected-faq-card {
  background-color: #171d3f !important;
}

div.not-selected-faq-card:hover {
  background-color: #232c5c !important;
}

.hero-etf-ml-default {
  margin-left: 1rem;
}

.hero-etf-ml1 {
  margin-left: 1.6rem;
}

.hero-etf-ml2 {
  margin-left: 2.2rem;
}

.hero-etf-ml3 {
  margin-left: 2.8rem;
}

.asset-icon {
  position: absolute;
  z-index: 1002;
}

.hover-item {
  position: relative;
  transform: translateY(50%);
  z-index: 1;
  transition: transform ease 0.5s;
}

.hover-container:hover .hover-item {
  transform: translateY(0%) !important;
}

.hover-card-text {
  background-color: #ffffff !important;
  border-radius: 1rem;
  height: 180px;
  width: 315px;
  z-index: 2;
}

a:hover {
  font-weight: bold;
}

.cta-input-button a:hover,
.cta-button a:hover {
  font-weight: initial;
}

.no-bold-hover:hover {
  font-weight: initial;
}

.learn-card {
  display: flex;
  flex-direction: column;
  margin-left: 1rem;
  margin-right: 1rem;
  margin-bottom: 4rem;
}

.column-gap-half {
  column-gap: 0.5rem !important;
}

.z-index-0 {
  z-index: 0 !important;
}

.z-index-1 {
  z-index: 1 !important;
}

.z-index-1040 {
  z-index: 1040;
}

.z-index-2000 {
  z-index: 2000;
}

.z-index-3000 {
  z-index: 3000;
}

.border-radius-24 {
  border-radius: 24px !important;
}

.border-radius-32 {
  border-radius: 32px;
}

.b-0 {
  bottom: 0;
}

#faq-card-arrow.selected-faq-card {
  animation-name: spin;
  animation-duration: 0.5s;
  transform-origin: 50% 50%;
  display: inline-block;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}

.truncated {
  overflow: hidden;
  display: block;
  width: 460px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-90deg);
  }
}

.space-bottom-referral-step-item {
  padding-bottom: 3rem;
}

.referral-step-item-icon {
  display: flex;
  background-color: #eceaff;
  border-radius: 50%;
  height: 120px;
  width: 120px;
  min-width: 120px;
  align-items: center;
  justify-content: center;
  margin-right: 2rem;
}

.referral-step-item-icon::after {
  border-left: 4px dotted #536ae3 !important;
  position: absolute !important;
  content: "";
  z-index: 2;
  height: 30px;
  margin-top: 170px;
}

.referral-step-item-icon-no-line::after {
  border-left: 0 !important;
}

.learn-step-item {
  margin-top: 0 !important;
}

.learn-step-item:hover .step-title {
  opacity: 1 !important;
  text-shadow: 0 0 1px black;
}

.learn-step-item span {
  font-family: "Noto Sans" !important;
  opacity: 1 !important;
  font-weight: 300;
}

.learn-step-item:hover .step-icon {
  color: #fff !important;
  background-color: #101327 !important;
}

.learn-step-item-current .step-title {
  opacity: 1 !important;
  text-shadow: 0 0 1px black;
}

.learn-step-item-current .step-icon {
  color: #fff !important;
  background-color: #101327 !important;
}

.learn-index-step-icon {
  background-color: transparent !important;
  border: 1px solid #101327 !important;
  height: 20px !important;
  width: 20px !important;
}

.learn-index-step-icon::after {
  border-left: 1px solid #101327 !important;
  top: 20px !important;
  height: calc(100% + 0.25rem) !important;
  left: 10px !important;
}

.learn-index-last-step-icon {
  background-color: transparent !important;
  border: 1px solid #101327 !important;
  height: 20px !important;
  width: 20px !important;
}

.learn-index-last-step-icon::after {
  border-left: none !important;
}

.vertical-line {
  border-left: 1px solid black;
  margin-left: 10px;
}

.galaxy-background {
  background-image: url(/svg/backgound.svg);
  background-repeat: no-repeat;
  background-position: center 100%;
  background-size: 3465px 3600px;
}

.footer-list {
  padding-left: 0;
  list-style: none;
}

.footer-list li {
  margin-bottom: 0.75rem;
  font-family: "Poppins", Roboto, sans-serif;
}

.footer-list a {
  color: rgba(255, 255, 255, 0.6);
}

.hero-shadow-lg {
  filter: drop-shadow(41px 35px 54px rgba(21, 23, 33, 0.24));
}

.new-faq-cards-accordion a {
  background-color: #171d3f;
  color: #fff;
  font-family: "Poppins", Roboto, sans-serif !important;
  font-size: 1.125rem !important;
}

.new-faq-cards-accordion .card-body {
  background-color: #171d3f !important;
  color: #fff !important;
  font-family: "Poppins", Roboto, sans-serif !important;
  font-size: 1rem !important;
  border-top: none !important;
}

.new-faq-cards-accordion .card {
  background-color: #171d3f !important;
}

.max-width-lg-60 {
  max-width: 60% !important;
}

.space-top-6 {
  padding-top: 20rem;
}

.pricing-card {
  min-width: 410px;
}

.card-collapse:hover {
  background-color: unset !important;
}

.max-w-100 {
  max-width: 100% !important;
}

.max-w-600px {
  max-width: 600px !important;
}

.dialog-position-class {
  max-width: 922px !important;
  top: 24vh !important;
}

.savings-interest-dialog-position-class {
  max-width: 600px !important;
  top: 17vh !important;
}

.h-280px {
  height: 280px;
}

.product-overview p {
  color: #70717d;
}

.product-overview p b {
  color: #101327;
  font-weight: initial !important;
}

.btn-link:hover {
  color: #fff !important;
  text-decoration: none !important;
}

.text-light-blue a {
  color: #546be5 !important;
}

#amazon-icon {
  top: 42%;
  left: -10%;
}

#apple-icon {
  top: 75%;
  left: -55%;
}

#coke-icon {
  top: 98%;
  left: -13%;
}

#google-icon {
  top: 45%;
  left: -45%;
}

#mcdonalds-icon {
  top: 105%;
  left: 20%;
}

#meta-icon {
  top: 120%;
  left: -10%;
}

#microsoft-icon {
  top: 110%;
  left: -30%;
}

#netflix-icon {
  top: 60%;
  left: -27%;
}

#nvidia-icon {
  top: 99%;
  left: 5%;
}

#spotify-icon {
  top: 82%;
  left: -28%;
}

#tesla-icon {
  top: 70%;
  left: -10%;
}

#twitter-icon {
  top: 24%;
  left: -16%;
}

#qr_code canvas {
  height: 260px;
  width: 260px;
}

@media (max-width: 576px) {
  h1 {
    font-size: 3.5rem !important;
  }

  h2 {
    font-size: 2.5rem !important;
  }

  h3 {
    font-size: 1.5rem !important;
  }

  h4 {
    font-size: 1.5rem !important;
  }

  h5 {
    font-size: 1.125rem !important;
  }

  .text-big {
    font-size: 1.125rem !important;
  }

  .text-medium {
    font-size: 1rem !important;
  }

  .text-regular {
    font-size: 0.875rem !important;
  }

  .text-small {
    font-size: 0.75rem !important;
  }

  .hero-etf-icon {
    display: none !important;
  }

  .galaxy-background {
    background-image: url(/svg/mobile-background.svg) !important;
    background-position: center bottom !important;
    background-size: auto !important;
  }

  .cta-button {
    width: 100% !important;
  }

  .max-w-xs-only-360px {
    max-width: 360px !important;
  }

  .pb-xs-only-100px {
    padding-bottom: 100px !important;
  }

  .pr-xs-only-140px {
    padding-right: 120px !important;
  }

  .hover-card-text {
    min-height: 140px !important;
    height: fit-content !important;
  }

  .truncated {
    width: 220px !important;
  }

  .hero-shadow-lg {
    filter: none;
  }

  #amazon-icon {
    top: 108%;
    left: 47%;
    z-index: 1;
  }

  #apple-icon {
    top: 36%;
    left: -1%;
    z-index: 1;
  }

  #coke-icon {
    top: 80%;
    left: 81%;
    z-index: 1;
  }

  #google-icon {
    top: 50%;
    left: 80%;
    z-index: 1;
  }

  #mcdonalds-icon {
    top: 95%;
    left: 85%;
    z-index: 1;
  }

  #meta-icon {
    top: 107%;
    left: 16%;
    z-index: 1;
  }

  #microsoft-icon {
    top: 77%;
    left: 2%;
    z-index: 1;
  }

  #netflix-icon {
    top: 99%;
    left: 66%;
    z-index: 1;
  }

  #nvidia-icon {
    top: 69%;
    left: 89%;
    z-index: 1;
  }

  #spotify-icon {
    top: 96%;
    left: 4%;
    z-index: 1;
  }

  #tesla-icon {
    top: 60%;
    left: -1%;
    z-index: 1;
  }

  #twitter-icon {
    top: 98%;
    left: 34%;
    z-index: 1;
  }
}

@media (min-width: 576px) {
  .h-md-auto {
    height: auto;
  }
}

@media (max-width: 800px) {
  .referral-step-item-icon {
    margin-right: 0 !important;
    margin-bottom: 1.5rem !important;
  }

  .referral-step-item-icon::after {
    border: 0 !important;
  }
}

@media (max-width: 991.98px) {
  /* The .galaxy-background moves up only at 992px */
  .galaxy-background {
    background-image: url(/svg/tablet-background.svg) !important;
    background-position: center bottom !important;
    background-size: auto !important;
  }
}

@media (max-width: 992px) {
  .bg-mobile-white {
    background-color: white !important;
  }

  .navbar-nav {
    border-radius: 0.5rem !important;
  }

  .navbar-nav-item button {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .max-width-lg-60 {
    max-width: none !important;
  }

  .truncated {
    width: 400px;
  }

  .navbar-nav-item .button-short {
    padding: 0.625rem 1.25rem;
  }

  .navbar-nav-item a {
    font-size: 0.875rem;
    padding-left: 0.05rem;
    padding-right: 0.05rem;
  }

  .navbar-nav-item {
    padding-left: 0.05rem;
    padding-right: 0.05rem;
  }
}

@media (max-width: 1080px) {
  .hover-item {
    transform: none;
    transition: none;
  }

  .hover-container:hover .hover-item {
    transform: none !important;
  }
}

@media (max-width: 1440px) {
  .d-xxl-only {
    display: none !important;
  }
}

@media (min-width: 1920px) {
  .space-bottom-xxl-2 {
    padding-bottom: 4rem !important;
  }

  .space-top-xxl-0 {
    padding-top: 0 !important;
  }

  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
}

/*------------------------------------
  Mini Projection noUi Slider
------------------------------------*/

#mini-projection .noUi-horizontal {
  height: 0.25rem !important;
  background-color: #546be533 !important;
  box-shadow: unset !important;
  border: 0 !important;
}

#mini-projection .noUi-handle {
  width: 1.45rem;
  height: 1.45rem;
  /* White */
  background: #ffffff;
  border: 0.5rem solid #546be5;
  transform: matrix(-1, 0, 0, 1, 0, 0);
}

#mini-projection .noUi-handle::before,
#mini-projection .noUi-handle::after {
  display: none !important;
}

#mini-projection .noUi-connect {
  background: #546be5;
}

/*------------------------------------
  Indicator Dots
------------------------------------*/
.u-indicator-dots {
  position: relative;
}

@media (min-width: 768px) {
  .u-indicator-dots::after {
    position: absolute;
    right: -2.1875rem;
    top: 50%;
    width: 2.4375rem;
    height: 0.75rem;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 55 16'%3E %3Ccircle fill='%23e7eaf3' cx='27.7' cy='8.1' r='7.7'/%3E %3Ccircle fill='%23e7eaf3' cx='5' cy='8.1' r='5'/%3E %3Ccircle fill='%23e7eaf3' cx='50' cy='8.1' r='5'/%3E %3C/svg%3E");
    background-repeat: no-repeat;
    content: "";
    margin-top: -0.375rem;
  }
}

/*------------------------------------
  Learning Guide Dynamic Content
------------------------------------*/

.guide-dynamic-content h3 {
  padding-top: 1.5rem !important;
  padding-bottom: 0.25rem !important;
}

.guide-dynamic-content h5 {
  font-family: "Noto Sans" !important;
  color: #11152e !important;
  font-weight: 800;
  font-size: 20px;
}

.guide-dynamic-content p {
  color: #11152e !important;
  font-family: "Noto Sans" !important;
  font-size: 14px;
  line-height: 1.5rem;
  font-weight: 300;
}

.guide-dynamic-content strong {
  color: #000;
  font-weight: 500;
}

.guide-dynamic-content img {
  border-radius: 15px;
  box-shadow: 0 3px 6px 0 rgba(140, 152, 164, 0.25) !important;
}

.guide-dynamic-content .gatsby-resp-image-background-image {
  background-size: 0;
  background-image: none;
}

/*------------------------------------
  noUi Slider
------------------------------------*/

.noUi-horizontal {
  height: 8px !important;
  background-color: #dce9f0 !important;
  box-shadow: none !important;
  border: 0 !important;
}

.noUi-handle {
  top: -10px !important;
  border-radius: 50%;
}

.noUi-handle::before,
.noUi-handle::after {
  display: none !important;
}

.noUi-connect {
  background: #36a2eb;
}

/*------------------------------------
  Netlify Customizations
------------------------------------*/

.netlify-identity-button {
  color: white !important;
}

/*------------------------------------
  Modal
------------------------------------*/

.early-access-modal .modal-dialog {
  max-width: 800px;
}

/*------------------------------------
  Markdown Page Customizations
------------------------------------*/

.markdown-page-body strong,
.markdown-page-body h1,
.markdown-page-body h2,
.markdown-page-body h3,
.markdown-page-body h4,
.markdown-page-body h5 {
  color: #101327 !important;
}

.markdown-page-body h4 {
  padding-top: 35px !important;
  padding-bottom: 25px !important;
}

.markdown-page-body a:hover {
  font-weight: initial;
}

.bg-bullet {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;

  width: 56.27px;
  height: 56.27px;

  /* White/100 */
  background: #ffffff;
  border-radius: 56.2667px;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

.w-475 {
  width: 100%;
  max-width: 475px;
}

.w-540 {
  width: 100%;
  max-width: 540px;
}

.w-750 {
  width: 100%;
  max-width: 750px;
}

.reward-logo {
  display: flex;
  justify-content: center;
}

.etfs-box-img {
  height: 280px;
  width: 280px;
}

@media (min-width: 600px) {
  .reward-logo {
    padding-top: 100px;
    padding-left: 275px;
  }

  .etfs-box-img {
    height: 420px;
    width: 420px;
  }
}

a {
  font-weight: 600 !important;
}

a:hover {
  font-weight: 600 !important;
  text-decoration: underline;
}

.modal-content {
  border-radius: 24px !important;
}

.learning-guides-dialog-position-class .modal-content {
  overflow: hidden;
}

@media (max-width: 760px){
  .modal-dialog.learning-guides-dialog-position-class{
    width: 100%;
    align-items: flex-end;
    min-height: unset;
    margin: 0;
    height: 100%;
  }
  .modal-dialog.learning-guides-dialog-position-class .modal-content{
    border-radius: 24px 24px 0 0 !important;
  }
  .modal-dialog.learning-guides-dialog-position-class h5{
    font-size: 1.5rem !important;
  }
}

.greece-waitlist-dialog-position-class .modal-content {
  overflow: hidden;
  border-radius: 16px !important;
}

.greece-waitlist-dialog-position-class.modal-dialog {
  max-width: 565px;
}

.greece-waitlist-input-container {
  display:flex;
  background-color: rgb(243, 243, 244);
  border-radius: 3.625em;
  border: 4px solid rgb(220, 226, 253);
}
.greece-waitlist-input-container input {
  background-color: #f3f3f4;
  border-radius: 3.625em 0 0 3.625rem;
  border: 1px solid #6e84f3;
  outline: none;
  margin-right: -30px;
}
@media (max-width: 568px) {
  .greece-waitlist-input-container {
    flex-direction: column;
    gap: 1.5em;
    border: none;
    background: none;
  }

  .greece-waitlist-input-container input {
    margin-right: 0;
    border-radius: 3.625em;
    outline: 4px solid rgb(220, 226, 253);
  }
}

.greece-waitlist-modal-mock {
  background-image: url("/img/learning-guide-modal-bg.png");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  padding: 2rem 5rem;
  max-height: 450px;
}

@media (max-height: 800px) {
  .greece-waitlist-modal-mock {
    max-height: 350px;
    padding: 1rem 5rem 0;
  }
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-light {
  border-color: #dce2fd !important;
}

.wh-coming-soon-card {
  background: #f1f3fd;
  color: #4957d5;
  border-radius: 6px;
  padding: 3px 6px;
}

.pointer {
  cursor: pointer;
}

.black-friday-promo-banner-container {
  z-index: 1030;
  height: 88px;
  max-height: 114px;
  background-color: #000;
  position: sticky;
  top: 0;
}

.mobile-banner-height {
  height: 114px !important;
}

.black-friday-promo-banner-desktop-image-container {
  max-width: 1290px;
  margin: 0 auto;
  background-image: url("../../static/img/black-friday-deal-extended.png");
  background-repeat: no-repeat;
  background-position: center 100%;
  background-size: contain;
}

.black-friday-mobile-modal-container {
  height: 80vh;
  margin: 0 auto !important;
  width: 45vh;
  margin-top: 8vh !important;
}

.black-friday-modal-image-container {
  max-width: 100%;
  max-height: 100%;
}

.close-modal-button {
  position: absolute;
  top: 10px;
  right: 10px;
  border: none;
  background: transparent;
  font-size: 2rem;
  color: #fff;
  cursor: pointer;
}

.cursor-pointer {
  cursor: pointer;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.5rem !important;
}

.gap-5 {
  gap: 2rem !important;
}

.gap-6 {
  gap: 3rem !important;
}

.gap-7 {
  gap: 4rem !important;
}

.fw-regular {
  font-weight: 400 !important;
}

.fw-bold {
  font-weight: 600 !important;
}

.fw-bolder {
  font-weight: 700 !important;
}

.blog-border-radious {
  border-radius: 16px;
}

.blog-font-family {
  font-family: "Noto Sans Display" !important;
}

.noto-sans {
  font-family: "Noto Sans" !important;
}

.work-sans {
  font-family: "Work Sans", Roboto, sans-serif;
}

.blog-font-family h5,
.blog-font-family h4 {
  font-family: "Noto Sans" !important;
}

.blog-font-family h3,
.blog-font-family h2,
.blog-font-family h1,
.blog-font-family p,
.blog-font-family span,
.blog-font-family div {
  font-family: "Noto Sans Display" !important;
}

.blog-text-dark {
  color: #000;
}

.blog-text-gray {
  color: #677788;
}

.blog-post-preview-image-container {
  height: 220px;
  max-height: 220px;
  width: auto;
  position:relative;
}

.blog-post-preview-image {
  display: block; /* Removes any default inline spacing */
  width: 100%; /* Ensures the image doesn't exceed container width */
  height: 100%; /* Ensures the image doesn't exceed container height */
  object-fit: cover; /* Keeps the aspect ratio of the image */
  border-radius: 1rem;
}

.email-input-container {
  background-color: #fff;
  border-radius: 32px;
}

.blog-search-container {
  background-color: #fff;
  border-radius: 32px;
  border: 1px solid rgba(16, 19, 39, 0.2);
}

.email-input,
.blog-search-input {
  outline: "none";
  flex-grow: 1;
  width: 100%;
}

.email-input,
.blog-search-input:focus {
  outline: none;
  border: none !important;
}

.text-up-to-two-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 120%;
}

.text-up-to-three-lines {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-up-to-four-lines {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-up-to-five-lines {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-post-preview-horizontal-header {
  font-size: 32px;
}

.blog-post-html-container {
  overflow: auto;
}

.blog-post-html-container ul {
  list-style-type: disc;
}

.blog-post-html-container a {
  color: #536ae3;
}

.blog-post-html-container ol {
  list-style-type: decimal;
}

.blog-post-html-container p,
.blog-post-html-container span,
.blog-post-html-container div {
  font-size: 18px !important;
  font-family: "Noto Sans Display" !important;
  font-weight: 300;
  color: #464646;
}

.blog-post-html-container p,
.blog-post-html-container b,
.blog-post-html-container strong {
  line-height: 32px;
}

.blog-post-html-container strong {
  color: black;
  font-weight: 500;
}

.blog-post-html-container b {
  color: black !important;
  font-weight: 500 !important;
}

.blog-post-html-container h1 {
  font-weight: 700 !important;
  padding-top: 24px;
  padding-bottom: 16px;
  margin: 0px;
}

.blog-post-html-container p {
  padding-bottom: 24px;
  margin: 0px;
}

.blog-post-html-container ul > li > p {
  padding-bottom: 8px;
}

.blog-post-html-container ul,
.blog-post-html-container ol {
  padding-left: 1.75rem;
  margin-bottom: 24px;
  margin-top: -8px;
}

.blog-post-html-container li > p {
  padding-bottom: 16px;
  margin: 0px;
}
.blog-post-html-container h2 {
  font-weight: 700 !important;
  padding-top: 24px;
  padding-bottom: 18px;
  margin: 0px;
}

.blog-post-html-container h3 {
  font-weight: 600 !important;
  padding-top: 16px;
  padding-bottom: 8px;
  margin: 0px;
}

.blog-post-html-container h4,
.blog-post-html-container h5,
.blog-post-html-container h6 {
  font-weight: 600 !important;
  padding-top: 10px;
  padding-bottom: 4px;
  margin: 0px;
}

.blog-post-html-container h1 {
  font-size: 32px !important;
}

.blog-post-html-container h2 {
  font-size: 28px !important;
}
.blog-post-html-container h3 {
  font-size: 24px !important;
}

.blog-post-html-container h4 {
  font-size: 20px !important;
}

.blog-post-html-container h5,
.blog-post-html-container h6 {
  font-size: 18px !important;
}

.blog-multiple-post-preview-three-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

.blog-multiple-post-preview-two-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
}

.blog-multiple-post-preview-mobile {
  flex: 1 0 0;
}
.blog-category-tag-container {
  position: absolute;
  display:block;
  left: 0.75rem;
  top: 0.75rem;
  background: #f4f4f4;
  padding: 4px 12px;
  border-radius: 2rem;
  font-size: 0.875rem;
}

.blog-category-tag-container:hover{
  cursor: pointer;
  color: #fff !important;
  background-color: #536ae3;
}

.author-avatar-image {
  border-radius: 50%;
  height: 100%;
  max-height: 100%;
  width: auto;
  object-fit: cover;
  object-position: center;
}

.author-avatar-container {
  height: 48px;
}

.author-image {
  border-radius: 50%;
  max-height: 100%;
  width: auto;
  object-fit: cover;
  object-position: center;
}

.blog-post-content-image-container {
  max-height: 100%;
  max-width: 100%;
  margin: 2rem 0;
}

.author-container {
  height: 88px;
}

.editors-pick-container {
  gap: 40px;
}

.editors-pick-post-container,
.category-post-container {
  gap: 40px;
}

.email-cta-container {
  background-image: url("/svg/starry-pattern.svg");
  background-size: cover; /* Covers the entire area of the div */
  background-position: center; /* Centers the background image */
}

.starry-pattern-background {
  position: relative;
  z-index: 1;
}

.starry-pattern-background::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;

  background:
    url("/svg/starry-pattern.svg") no-repeat,
    #11152e;
  background-size: cover;
  background-position: center; /* Centers the background image */
  opacity: 0.8; /* Adjust this value for desired opacity of the background */
}

.white-background {
  background-color: #fff;
}

.blog-header-title {
  color: #000;
  font-size: 2.5rem !important;
  font-weight: 700;
  line-height: 120%;
}
.blog-directory-title {
  color: #000;
  font-size: 2.5rem !important;
  font-weight: 700;
  line-height: 120%;
}

.tags-container {
  flex: 0 0 20%;
}

.author-socials-icon-list {
  padding-left: 0;
  list-style: none;
}

.author-socials-icon-list li {
  margin-bottom: 0.75rem;
  font-family: "Poppins", Roboto, sans-serif;
}

.no-underline-link:hover {
  text-decoration: none !important;
}

.stick-element-to-top {
  position: sticky;
  top: 15px;
}

.blog-breadcrump {
  text-decoration: none !important;
  cursor: pointer;
  font-weight: 400 !important;
}

.blog-breadcrump:hover {
  text-decoration: none !important;
  color: #536ae3;
}

.blog-table-of-contents-list-item {
  font-style: normal;
  /* 16px */
  font-size: 1rem;
  line-height: 140%;
  text-decoration: none;
  font-weight: 400;
  cursor: pointer;
  color: #10132799;
}

.blog-table-of-contents {
  list-style-type: none;
}

.blog-table-of-contents-list-item.active {
  color: #000;
  font-weight: 600 !important;
}

.blog-table-of-contents-list-item:hover {
  text-decoration: none;
  color: #000 !important;
  font-weight: 500 !important;
}

.blog-table-of-contents-list-item.active:hover {
  color: #000;
  font-weight: 600 !important;
}

.blog-table-of-contents-button-box-shadow {
  box-shadow: 0px -8px 15px 0px rgba(0, 0, 0, 0.11);
}

.blog-table-of-contents-mobile-container {
  box-shadow: 0px 0px 10px 10px rgba(27, 39, 100, 0.24) !important;
  max-height: 100vh; /* Adjust this value based on your needs */
  overflow-y: auto; /* Enables vertical scrolling */
}

.top-corner-border-radius {
  border-radius: 16px 16px 0px 0px;
}

.blog-post-form-title {
  line-height: 140%;
  font-size: 1.5rem;
}

.blog-post-form {
  background: #536ae3;
  border-radius: 32px;
}

.button-short.button-width-100 {
  width: 100% !important;
}

@media (min-width: 1200px) {
  .blog-post-content-container {
    max-width: 1320px;
  }
}

@media (min-width: 1900px) {
  .blog-post-content-container {
    max-width: 1540px;
  }
}

@media (min-width: 992px) {
  .blog-post-html-container {
    flex: 0 0 59%;
    max-width: 100%;
  }
}

.blog-post-table-of-contents-container {
  flex: 0 0 18%;
  padding: 0px;
}

.blog-post-author-container {
  flex: 0 0 23%;
  padding: 0px;
}

.blog-post-button {
  border: none !important;
  border-radius: 32px !important;
  width: fit-content !important;
  height: 45px !important;
  padding: 0.625rem 2rem;
}

.blog-post-button.embedded {
  display: block;
  margin: 0 auto;
}

.blog-post-button:hover {
  cursor: pointer;
}

/* General Table Styling */
.blog-post-html-container table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 0 1em 0;
  display: block;
  overflow-x: auto;
  font-family: Arial, sans-serif;
  color: #333; /* Adjust text color as needed */
}

.blog-post-html-container table p {
  font-size: 1rem !important;
}
/* Header Styling */
.blog-post-html-container thead th {
  background-color: #f2f2f2; /* Light grey background */
  color: #333; /* Dark text for contrast */
  font-weight: bold;
  text-align: center;
  padding: 10px;
  border-bottom: 2px solid #e0e0e0; /* Subtle border for separation */
}

/* Body Styling */
.blog-post-html-container tbody td {
  padding: 10px;
  text-align: left; /* Align text to the left */
  border-bottom: 1px solid #e0e0e0; /* Light border for each row */
}

/* Row Styling */
.blog-post-html-container tbody tr:nth-child(even) {
  background-color: #f9f9f9; /* Slightly different background for alternate rows */
}

.blog-post-html-container tbody tr:hover {
  background-color: #e9e9e9; /* Subtle hover effect */
}

/* Responsive Table */
@media screen and (max-width: 600px) {
  .blog-post-html-container table {
    overflow-x: scroll;
  }
  .blog-post-html-container p {
    font-size: 0.85rem !important;
  }
}

@keyframes slideUp {
  0%,
  13% {
    opacity: 0;
    transform: translateY(100%);
  }
  13% {
    opacity: 1;
    transform: translateY(0);
  }
  95% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-100%);
  }
}

.animated-text-container {
  position: relative;
  min-height: 72px; /* Changed from fixed height to min-height */
  overflow: visible; /* Changed from hidden to visible */
  width: 100%;
}

.animated-text {
  animation: slideUp var(--animation-duration) infinite;
  width: 100%;
  word-wrap: break-word;
  white-space: normal; /* Allow text to wrap */
}

.curved-underline {
  position: relative;
  margin-right: 1rem;
}

.curved-underline:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 11px;
  width: 100%;
  border: solid 5px;
  border-color: rgb(83, 106, 227) transparent transparent transparent;
  border-radius: 70%;
}

@keyframes fillBackground {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0%;
  z-index: 0;
}

/* Only apply animation and background color on desktop (screens >= 992px) */
@media (min-width: 992px) {
  .overlay {
    background-color: linear-gradient(
        90deg,
        #536ae3 -0.06%,
        rgba(83, 106, 227, 0) -0.06%
      ),
      #11152e;
    animation: fillBackground var(--animation-duration-tabs) linear forwards;
  }
}

.inter {
  font-family: "Inter" !important;
}

.hide-scrollbar {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* WebKit-based browsers */
}

.gold-plan-styles {
  background: radial-gradient(
      79.08% 407.86% at 100% 0%,
      #db8a5c 0%,
      #d07b7b 100%
    )
    /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.intercom-image-shadow {
  filter: drop-shadow(10px 10px 40px rgba(0, 0, 0, 0.08))
    drop-shadow(5px 14px 80px rgba(26, 26, 26, 0.12));
}

.image-carousel-container {
  width: inherit;
}

.learning-guide-container * {
  font-family: "Noto Sans" !important;
}

.learning-guide-container p {
  margin: 1em 0;
}

.learning-guide-container ul,
.learning-guide-container ol {
  padding-left: 40px;
  margin: 1em 0;
}

.learning-guide-container ul {
  list-style-type: disc;
}

.learning-guide-container ol {
  list-style-type: decimal;
}

.learning-guide-container li {
  margin: 0.5em 0;
}

.learning-guide-card-title {
  color: #101327 !important;
}

.learning-guide-card-description {
  color: #11152e !important;
}

.asset-page h5,
h4,
h3,
h2,
h1,
h6 {
  font-family: "Noto Sans" !important;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  direction: rtl;
}

.progress {
  height: 100%;
  border-radius: 0 10px 10px 0;
  position: absolute;
  right: 0;
}

.progress-bar-left {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}
.progress-left {
  height: 100%;
  border-radius: 0 10px 10px 0;
  position: absolute;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.legal-content h1 {
  font-size: 30px !important;
  margin-top: 2.5em;
  margin-bottom: 1em;
  line-height: 1.5em;
}

.legal-content li,
.legal-content p,
.legal-content span {
  font-weight: 300;
}

.legal-contenth1 a,
.legal-contenth1 h1 a,
.legal-contenth1 h2 a {
  color: inherit;
  text-decoration: none;
  font-weight: inherit;
}

.legal-content > *:first-child {
  margin-top: 0; /* or any value you want to set */
}

.legal-content h1 a:hover,
.legal-contenth1 h2 a:hover {
  color: inherit;
  text-decoration: none;
}

.legal-content > ul {
  list-style-type: none !important;
  padding-left: 0px;
  margin-top: 0em;
  margin-bottom: 1.5em;
}

.legal-content > ol {
  list-style-type: none !important;
  padding-left: 20px !important;
  margin-top: 1.5em !important;
  margin-bottom: 0.5em !important;
}

/* 1st indetation level styles */

.legal-content > ul > li {
  margin-bottom: 2em;
}

.legal-content ul,
.legal-content ol {
  list-style-type: none !important; /* Removes numbers */
  /* margin-bottom: 0.75rem; */
  padding-left: 0px; /* Keeps indentation */
  margin-bottom: 1.5em;
}

/* 2nd indetation level styles */
.legal-content > ul > li > ul > li,
.legal-content > ol > li > ol > li {
  margin-bottom: 1.5em;
}

.legal-content ul ul,
.legal-content ol ol {
  list-style-type: none !important; /* Removes numbers */
  /* margin-bottom: 0.75rem; */
  padding-left: 20px; /* Keeps indentation */
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

/* 3rd indetation level styles */
.legal-content > ul > li > ul > li > ul > li,
.legal-content > ol > li > ol > li > ol > li {
  margin-bottom: 1.5em;
}

.legal-content ul ul ul,
.legal-content ol ol ol {
  list-style-type: none !important; /* Removes numbers */
  /* margin-bottom: 0.75rem; */
  padding-left: 20px; /* Keeps indentation */
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

/* 4th indetation level styles */
.legal-content > ul > li > ul > li > ul > li > ul > li,
.legal-content > ol > li > ol > li > ol > li > ol > li {
  margin-bottom: 1em;
}

.legal-content ul ul ul ul,
.legal-content ol ol ol ol {
  list-style-type: none !important; /* Removes numbers */
  /* margin-bottom: 0.75rem; */
  padding-left: 20px; /* Keeps indentation */
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.legal-content li {
  /* margin-bottom: 0.5rem; */

  margin-bottom: 0.75em; /* Adjusts space between list items */
  line-height: 1.5em; /* Custom line height for list items */
}

.legal-content p {
  margin-bottom: 1rem;
}

.legal-content strong {
  color: black;
  font-weight: 500;
}

.active-glossary-index {
  padding: 6px 12px;
  margin: 0px 2px;
  text-decoration: none;
  color: #536ae3;
  background-color: #f1f3fd;
  border-radius: 8px;
  font-weight: normal !important;
}

.active-glossary-index:hover {
  text-decoration: none;
}

.glossary-index {
  padding: 6px 12px;
  text-decoration: none;
  margin: 0px 2px;
  color: #757575;
  font-weight: normal !important;
}

.glossary-index:hover {
  cursor: pointer;
  text-decoration: none;
  color: #536ae3 !important;
  background-color: #f1f3fd !important;
  border-radius: 8px;
}

.glossary-content p {
  margin-bottom: 1.5rem;
}

.glossary-content strong {
  color: black;
  font-weight: 500;
}

.glossary-content h2 {
  font-size: 28px !important;
  margin-top: 2.5em;
  margin-bottom: 1em;
  font-weight: 800;
  line-height: 33.3px;
}

.glossary-content li,
.glossary-content p,
.glossary-content span {
  font-weight: 300;
}

.glossary-content a,
.glossary-content h1 a,
.glossary-content h2 a {
  color: inherit;
  text-decoration: none;
  font-weight: inherit !important;
}

.glossary-content > *:first-child {
  margin-top: 0; /* or any value you want to set */
}

.glossary-content h1 a:hover,
.glossary-contenth1 h2 a:hover {
  color: inherit;
  text-decoration: none;
}

.glossary-toc-border {
  border-right: 2px solid #dce2fd;
}

.disable-anchor-style {
  color: inherit;
  text-decoration: none;
  font-weight: inherit;
}
.disable-anchor-style:hover {
  color: inherit;
  text-decoration: none;
}

.help-centre-anchor {
  text-decoration: none;
  color: #757575;
  font-weight: 400;
  font-size: 14px;
}

.help-centre-anchor:hover {
  text-decoration: none;
  color: #546be5;
}

.white-bold strong {
  color: white !important;
  font-weight: 500;
}

.help-centre-content h1 {
  font-size: 30px !important;
  margin-top: 2.5em;
  margin-bottom: 1em;
  line-height: 1.5em;
}

.help-centre-content li,
.help-centre-content p,
.help-centre-content span {
  font-weight: 300;
}

.help-centre-contenth1 a,
.help-centre-contenth1 h1 a,
.help-centre-contenth1 h2 a {
  color: inherit;
  text-decoration: none;
  font-weight: inherit;
}

.help-centre-content > *:first-child {
  margin-top: 0; /* or any value you want to set */
}

.help-centre-content h1 a:hover,
.help-centre-contenth1 h2 a:hover {
  color: inherit;
  text-decoration: none;
}

/* 1st indetation level styles */
.help-centre-content > ul {
  list-style-type: disc !important;
  padding-left: 20px !important;
  margin-top: 0em;
  margin-bottom: 1.5em;
}

.help-centre-content > ol {
  list-style-type: upper-alpha !important;
  padding-left: 25px !important;
  margin-top: 1.5em !important;
  margin-bottom: 0.5em !important;
}

.help-centre-content > ol > li > ul {
  list-style-type: disc !important;
  padding-left: 20px !important;
  margin-top: 1.5em !important;
  margin-bottom: 0.5em !important;
}

.help-centre-content > ol > li {
  /* list-style-type: disc !important; */
  padding-left: 5px !important;
  /* margin-top: 1.5em !important; */
  margin-bottom: 2em;
}

.help-centre-content > ol > li > ul > li {
  margin-bottom: 2em;
}

.help-centre-content > ul > li {
  margin-bottom: 2em;
}

/* 2nd indetation level styles */
.help-centre-content > ul > li > ul > li,
.help-centre-content > ol > li > ol > li {
  margin-bottom: 1.5em;
}

.help-centre-content ul ul,
.help-centre-content ol ol {
  list-style-type: disc !important;
  padding-left: 20px;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.help-centre-content p {
  margin-bottom: 1rem;
}

.help-centre-content strong {
  color: black;
  font-weight: 500;
}

@media (max-width: 576px) {
  .animated-text-container {
    min-height: 95px;
  }
  
  h2 {
    font-size: 2.5rem !important;
    word-wrap: break-word;
    white-space: normal;
  }
}
