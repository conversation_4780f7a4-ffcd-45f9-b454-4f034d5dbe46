import React from "react"
import Seo from "../../components/seo"
import Layout from "../../components/layout"
import { graphql } from "gatsby"
import BlogPostPreviewHorizontal from "../../components/blog/blog-post-preview-horizontal"
import EmailCta from "../../components/blog/email-cta"
import BlogPostCategoryPreview from "../../components/blog/blog-post-category-preview"
import LocaleEnum from "../../configs/locale-config"
import withStaticPagePathname from "../../hoc/withStaticPagePathname"

class BlogPage extends React.Component {
  #groupAndLimitPostsByCategory = (posts, limit) => {
    const postsByCategory = {}
    const categoryNames = new Set() // Create a Set to store unique category names

    posts.forEach((post) => {
      const categoryName = post.category.name
      categoryNames.add(categoryName) // Add category name to the Set

      if (!postsByCategory[categoryName]) {
        postsByCategory[categoryName] = []
      }

      if (postsByCategory[categoryName].length < limit) {
        postsByCategory[categoryName].push(post)
      }
    })

    return { postsByCategory, categoryNames: Array.from(categoryNames) } // Convert Set to Array
  }

  render() {
    const allPosts = this.props.data.allContentfulPostsBlog.nodes
    const { postsByCategory } = this.#groupAndLimitPostsByCategory(allPosts, 7)
    const title = "Επενδύσεις στην Ελλάδα. Μετοχές, ETFs και Κρυπτονομίσματα"
    const description =
      "Μάθε τα πάντα για τον κόσμο των επενδύσεων. Ανακάλυψε πως μπορείς να αγοράσεις μετοχές και ETFs αλλά και πως να χτίσεις ένα χαρτοφυλάκιο με μακρύ ορίζοντα."

    return (
      <Layout activePage={"blog"} backgroundColor={"#fff"}>
        <Seo
          description={description}
          title={title}
          hrefLangs={[
            { url: "/el/blog/", languageCode: "el" },
            { url: "/uk/blog/", languageCode: "en" },
            { url: "/uk/blog/", languageCode: "x-default" },
          ]}
          lang={"el"}
        />

        <div className="blog-font-family px-2 px-md-0">
          {/* Main Blog Post */}
          <div className="container space-top-2 space-bottom-2 mb-4">
            <BlogPostPreviewHorizontal
              item={allPosts[allPosts.length - 1]}
              hideImage={false}
              locale={LocaleEnum.Greece}
            />
          </div>
          {/* CTA */}
          <div className="container space-2 my-3">
            <EmailCta locale={LocaleEnum.Greece} />
          </div>
          {/* For each category, display the BlogPostCategoryPreview component */}
          {Object.entries(postsByCategory)
            .filter(([_, posts]) => posts.length > 0)
            .map(([categoryName, posts]) => (
              <BlogPostCategoryPreview
                key={categoryName}
                className="container space-bottom-2 mb-4"
                categoryName={categoryName}
                blogPosts={posts}
                categorySlug={posts[0]?.category?.slug}
                locale={LocaleEnum.Greece}
              />
            ))}
        </div>
      </Layout>
    )
  }
}

export default withStaticPagePathname(BlogPage)

export const query = graphql`
  {
    allContentfulPostsBlog(
      filter: { locale: { code: { eq: "el" } } }
      sort: { createdAt: DESC }
    ) {
      nodes {
        slug
        title
        metaDescription
        readingTime
        image {
          file {
            url
          }
        }
        category {
          name
          color
          slug
        }
        author {
          name
          slug
          image {
            file {
              url
            }
          }
        }
        date
      }
    }
  }
`
