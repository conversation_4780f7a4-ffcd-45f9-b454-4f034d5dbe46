import { DictionaryMappingEnum } from "./translation-entries"

export const DICTIONARY_UK = {
  [DictionaryMappingEnum.BLOG_CTA]: "Download the app",
  [DictionaryMappingEnum.EMAIL_CTA_TITLE]:
    "Subscribe to Wealthyhood's investing newsletter ",
  [DictionaryMappingEnum.EMAIL_CTA_SUCCESS]: "Thank you for subscribing!",
  [DictionaryMappingEnum.EMAIL_CTA_ERROR]:
    "There was an error submitting the form. Please try again.",
  [DictionaryMappingEnum.READ_POST]: "Read Post",
  [DictionaryMappingEnum.READ]: "read",
  [DictionaryMappingEnum.VIEW_ALL_POSTS]: "View all Posts",
  [DictionaryMappingEnum.ALL_POSTS]: "All Posts",
  [DictionaryMappingEnum.LATEST_POSTS]: "Latest Posts",
  [DictionaryMappingEnum.DIRECTORY]: "Directory",
  [DictionaryMappingEnum.DIRECTORY_HEADER]:
    "Get the latest information about the investing world",
  [DictionaryMappingEnum.TABLE_OF_CONTENTS]: "Table Of Contents",
  [DictionaryMappingEnum.OPEN_TABLE_OF_CONTENTS]: "Open table of contents",
  [DictionaryMappingEnum.CLOSE_TABLE_OF_CONTENTS]: "Close table of contents",
  [DictionaryMappingEnum.BLOG_POST_FORM_TITLE]: "Join the Investors' Club",
  [DictionaryMappingEnum.BLOG_POST_FORM_SUBTITLE]:
    "Get access to professional investing tools for free and start building your wealth today!",
  [DictionaryMappingEnum.BLOG_DISCLAIMER]:
    "This article is for informational and educational purposes only and does not constitute investment advice. Every investment carries risk. The value of your investments may decrease or increase. Wealthyhood does not provide investment, financial, legal, tax, or accounting advice. Tax treatment depends on each investor’s personal circumstances and may change in the future. Past performance does not guarantee future results. You should take your personal circumstances into account when making investment decisions and, if necessary, consult professional investment advisors.",
  [DictionaryMappingEnum.CAPITAL_AT_RISK]: "Capital at risk",
  [DictionaryMappingEnum.START_INVESTING]: "Start investing",
  [DictionaryMappingEnum.RELEVANT_POSTS]: "Relevant Posts",
  [DictionaryMappingEnum.FROM_THE_AUTHOR]: "From the author",
  [DictionaryMappingEnum.WHO_IS]: "Who is",
}
