import { navigate } from "gatsby"
import {
  LanguageEnum,
  RegionEnum,
  REGION_TO_DEFAULT_LOCALE,
} from "../configs/language-region-config"
import LocaleEnum from "../configs/locale-config"

/**
 * Get the display code of a language
 *
 * @param {string} language Language code
 * @returns {string} Display code of the language
 */
export const getLanguageDisplayCode = (language) => {
  switch (language) {
    case LanguageEnum.English:
      return "EN"
    case LanguageEnum.Greek:
      return "EL"
  }
}

/**
 * Get the display flag of a language
 *
 * @param {string} language Language code
 * @returns {string} Display flag of the language
 */
export const getLanguageDisplayFlag = (language) => {
  switch (language) {
    case LanguageEnum.English:
      return "english_flag.png"
    case LanguageEnum.Greek:
      return "greek_flag.png"
  }
}

/**
 * Get the display name of a language
 *
 * @param {string} language Language code
 * @returns {string} Display name of the language
 */
export const getLanguageDisplayName = (language) => {
  switch (language) {
    case LanguageEnum.English:
      return "English"
    case LanguageEnum.Greek:
      return "Ελληνικά"
  }
}

/**
 * Get the language from a language-region pair
 *
 * @param {string} languageRegion Language-region pair
 * @returns {string} Language
 */
export const getLanguageFromLanguageRegion = (languageRegion) => {
  const [language] = languageRegion.split("-")
  return language || LanguageEnum.English
}

/**
 * Get the locale from a language-region pair
 *
 * @param {string} languageRegion Language-region pair
 * @returns {string} Locale
 */
export const getLocaleFromLanguageRegion = (languageRegion) => {
  const [, region] = languageRegion.split("-")

  return REGION_TO_DEFAULT_LOCALE[region.toLowerCase()] || LocaleEnum.EU
}

/**
 * Get the region from a language-region pair
 *
 * @param {string} languageRegion Language-region pair
 * @returns {string} Region
 */
export const getRegionFromLanguageRegion = (languageRegion) => {
  const [, region] = languageRegion.split("-")
  return region
}

/**
 * Get the region from URL prefix
 * Handles formats:
 * - language-region (e.g., en-gr, el-gr) → returns region part ('gr')
 * - direct locale 'eu' → returns 'eu'
 * - direct locale 'uk' → returns 'gb'
 * - language-only (e.g., en, el) → returns defaultRegion parameter
 * - null prefix → returns null
 *
 * @param {string} urlPrefix The URL prefix to parse
 * @param {string} defaultRegion The default region to return for language-only prefixes
 * @returns {string|null} The region code ('gr', 'eu', 'gb'), defaultRegion, or null
 */
export const getRegionFromUrlPrefix = (urlPrefix, defaultRegion) => {
  if (!urlPrefix) return null

  // Handle language-region format (e.g., en-gr, el-gr)
  if (urlPrefix.includes("-")) {
    return getRegionFromLanguageRegion(urlPrefix)
  }

  // Handle direct locale format (eu, uk)
  if (urlPrefix === LocaleEnum.EU) {
    return RegionEnum.EU
  } else if (urlPrefix === LocaleEnum.UK) {
    return RegionEnum.UK
  }

  // we return default in case we are in a language-only url prefix
  return defaultRegion
}

// this should be used only for language picker, will be populated in the future with other countries
export const shouldDisplayLanguagePicker = (urlPrefix, region) => {
  return region === RegionEnum.Greece && urlPrefix.includes("-")
}

export const isRegionPrefixGreece = (urlPrefix, region) => {
  return region === RegionEnum.Greece && urlPrefix.includes("-")
}

export const navigateToLanguage = (language, callbackFn) => {
  let path = typeof window !== "undefined" ? window.location.pathname : "/"

  // Remove leading and trailing slashes and split path
  path = path.replace(/^\/|\/$/g, "")
  const pathSegments = path.split("/")

  // Get the current URL prefix from the first segment
  const currentPrefix = pathSegments[0]
  let newPrefix

  // Determine if current URL uses language-region format
  if (currentPrefix && currentPrefix.includes("-")) {
    // If current format is language-region, keep it but update the language
    const [, currentRegion] = currentPrefix.split("-")
    newPrefix = `${language}-${currentRegion}`
  } else {
    // If current format is language-only, keep it that way
    newPrefix = language
  }

  // Remove the current prefix from path segments
  pathSegments.shift()

  // Reconstruct the path
  const newPath =
    pathSegments.length > 0
      ? `/${newPrefix}/${pathSegments.join("/")}`
      : `/${newPrefix}`

  callbackFn()
  navigate(newPath)
}
