import { DictionaryEnum } from "../translations/dictionary-entries"

export const HEADER_LINKS = [
  {
    label: DictionaryEnum.COMMON_NAV_INVEST,
    slug: "",
    domID: "product-overview-section",
    slugsToBeActive: ["index"],
    type: "scroll",
    pageToBeScrollable: "index",
  },
  {
    label: DictionaryEnum.COMMON_NAV_LEARN,
    slug: "learning-guides/",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_NAV_PRICING,
    slug: "pricing/",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_NAV_HELP,
    slug: "help-centre/",
    type: "link",
  },
]

export const FOOTER_LINKS = [
  {
    label: DictionaryEnum.COMMON_FOOTER_INVEST,
    slug: "#invest",
    domID: "invest",
    type: "scroll",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_LEARN,
    slug: "learning-guides/",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_WHY_WEALTHYHOOD,
    slug: "#why-wealthyhood",
    domID: "why-wealthyhood",
    type: "scroll",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_CALCULATOR,
    slug: "#calculator",
    domID: "calculator",
    type: "scroll",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_MONEY_SAFE,
    slug: "#money-safe",
    domID: "money-safe",
    type: "scroll",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_CUSTOMER_SERVICE,
    slug: "#not-alone",
    domID: "not-alone",
    type: "scroll",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_PRICING,
    slug: "pricing/",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_REFER_FRIEND,
    slug: "referrals/",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_HELP_CENTER,
    slug: "help-centre/",
    domID: "help-centre",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_GLOSSARY,
    slug: "glossary/",
    type: "link",
    languageOnlyPrefix: true,
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_FINANCIAL_STATEMENTS,
    slug: "financial-statements/",
    type: "link",
  },
  {
    label: DictionaryEnum.COMMON_FOOTER_PRESS_RELEASES,
    slug: "press-releases/",
    type: "link",
    languageOnlyPrefix: true,
  },
]
