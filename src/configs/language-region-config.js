/**
 * Enum for supported languages in the application
 */
const LanguageEnum = {
  English: "en",
  Greek: "el",
}

const RegionEnum = {
  Greece: "gr",
  EU: "eu",
  UK: "gb",
}

const REGION_TO_DEFAULT_LANGUAGE = {
  GR: LanguageEnum.Greek,
  GB: LanguageEnum.English,
}

const REGION_TO_DEFAULT_LOCALE = {
  [RegionEnum.Greece]: "eu",
  [RegionEnum.UK]: "uk",
}

const SUPPORTED_LANGUAGE_REGION_PAIRS = ["en-GR", "el-GR"]

module.exports = {
  REGION_TO_DEFAULT_LANGUAGE,
  REGION_TO_DEFAULT_LOCALE,
  SUPPORTED_LANGUAGE_REGION_PAIRS,
  LanguageEnum,
  RegionEnum,
}
