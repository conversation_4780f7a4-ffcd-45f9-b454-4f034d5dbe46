import React from "react"
import { documentToHtmlString } from "@contentful/rich-text-html-renderer"
import { BLOCKS, INLINES } from "@contentful/rich-text-types"
import slugify from "../utils/slugify"
import Layout from "../components/layout"
import Seo from "../components/seo"
import BlogAuthorPreview from "../components/blog/blog-author-preview"
import TableOfContents from "../components/blog/blog-post-table-of-contents"
import BlogPostMobileCTA from "../components/blog/blog-post-mobile-cta"
import BlogAuthor from "../components/blog/blog-author"
import DownloadAppBtn from "../components/download-app-btn"
import EmailCta from "../components/blog/email-cta"
import BlogMultiplePostPreview from "../components/blog/blog-multiple-post-preview"
import { formatDateToLongFormWithoutComma } from "../utils/date"
import { Link } from "gatsby"
import { translate } from "../translations/translator"
import { DictionaryMappingEnum } from "../translations/translation-entries"
import LocaleEnum from "../configs/locale-config"
import withPathname from "../hoc/withPathname"

const tocTagRegex = /<toc>(.*?)<\/toc>/
class BlogPostTemplate extends React.Component {
  // This is for cases where the heading might be partially underlined and have two child nodes
  // so you can't just get the text (.value) from the first one in the array
  #getPlainTextFromHeader = (contentNode) => {
    return contentNode.reduce((acc, current) => {
      return acc + current.value
    }, "")
  }

  #getHeadersFromNodes = (nodesArray) => {
    return nodesArray
      .filter((node) => {
        const isHeading =
          node.nodeType === BLOCKS.HEADING_2 ||
          node.nodeType === BLOCKS.HEADING_3
        const fullText = this.#getPlainTextFromHeader(node.content)
        const containsTocTag = tocTagRegex.test(fullText)

        return isHeading && containsTocTag
      })
      .map((heading) => {
        const fullText = this.#getPlainTextFromHeader(heading.content)
        const plainText = fullText.match(tocTagRegex)[1].trim() // Extract text within <toc> tags

        return {
          text: plainText,
          href: `#${slugify(plainText)}`,
        }
      })
  }

  // Function to render heading nodes (H2 and H3)
  #renderHeadingNode = (node, headingType) => {
    const initialPlainText = this.#getPlainTextFromHeader(node.content)
    const tocMatch = initialPlainText.match(tocTagRegex)

    let idAttribute = ""
    if (tocMatch) {
      const textInsideTocTags = tocMatch[1].trim()
      idAttribute = ` id=${slugify(textInsideTocTags)}`
    }

    return `<${headingType} ${idAttribute}>${initialPlainText}</${headingType}>`
  }

  #transformRichTextToHTML = (richTextDocument, imageReferences, locale) => {
    const options = {
      renderNode: {
        [INLINES.EMBEDDED_ENTRY]: (node) => {
          let button = imageReferences.find(
            (button) => button.contentful_id === node.data.target.sys.id
          )
          return `<a
          class="blog-post-button inline"
          href=${button.href}
          target="_blank"
          style="color: ${button.textColor}; margin: 0 auto; background-color: ${button.backgroundColor};">
           ${button.label} </a>`
        },
        [INLINES.HYPERLINK]: (node) => {
          // Check if the URI is external, excluding 'wealthyhood.com' from being considered external
          const isExternalLink =
            /^(http|https|www)|^\/\//i.test(node.data.uri) &&
            !node.data.uri.includes("wealthyhood.com")
          // Open links in a new tab if they are external
          return `<a href="${node.data.uri}" ${
            isExternalLink ? "target='_blank'" : ""
          } rel="noopener noreferrer">${
            (node.content[0]?.value ?? "") + (node.content[1]?.value ?? "")
          }</a>`
        },
        [BLOCKS.EMBEDDED_ENTRY]: (node) => {
          let button = imageReferences.find(
            (button) => button.contentful_id === node.data.target.sys.id
          )
          return `<a
          class="blog-post-button embedded"
          href=${button.href}
          target="_blank"
          style="color: ${button.textColor}; background-color: ${button.backgroundColor};">
           ${button.label} </a>`
        },
        // We find the relevant image info by associating the node.data.target.sys.id to the relavant image reference to get the url and to include it inside the HTML
        // as <img> tags.
        [BLOCKS.EMBEDDED_ASSET]: (node) => {
          let image = imageReferences.find(
            (image) => image.contentful_id === node.data.target.sys.id
          )
          return `<img
        class="blog-post-content-image-container"
        src=${image.url} alt=${image.title}/>`
        },
        [BLOCKS.HEADING_2]: (node) => this.#renderHeadingNode(node, "h2"),
        [BLOCKS.HEADING_3]: (node) => this.#renderHeadingNode(node, "h3"),
      },
    }

    let htmlString = documentToHtmlString(richTextDocument, options)

    // Removing <toc> and <toc/> tags from the HTML string
    htmlString = htmlString.replace(/<toc>/g, "").replace(/<toc\/>/g, "")

    // Add disclaimer
    const disclaimer = translate(DictionaryMappingEnum.BLOG_DISCLAIMER, locale)
    htmlString += `
                  <p></p>
                  <p></p>
                  <p>&#8212;</p>
                  <p></p>
                  <p></p>
                  <p>
                    <strong>Disclaimer: </strong>${disclaimer}
                  </p>`

    return htmlString
  }

  #generateSchemaOrgMarkup = (
    title,
    imageUrl,
    authorName,
    authorSlug,
    createdAt,
    updatedAt,
    description,
    canonicalUrl,
    category,
    wordCount,
    faqs,
    howTo,
    locale
  ) => {
    let faqEntities = []
    if (faqs && faqs.length > 0) {
      faqEntities = faqs.map((faq) => ({
        "@type": "Question",
        name: faq.question,
        acceptedAnswer: {
          "@type": "Answer",
          text: faq.answer,
        },
      }))
    }

    let howToSteps = []
    if (howTo && howTo.steps && howTo.steps.length > 0) {
      howToSteps = howTo.steps.map((step) => ({
        "@type": "HowToStep",
        name: step.question,
        text: step.answer,
      }))
    }

    const schema = {
      "@context": "http://schema.org",
      "@type": "BlogPosting",
      headline: title,
      image: [imageUrl],
      author: {
        "@type": "Person",
        name: authorName,
        url: `https://wealthyhood.com/${locale}/blog/${authorSlug}/`,
      },
      datePublished: createdAt,
      dateModified: updatedAt,
      description: description,
      url: canonicalUrl,
      publisher: {
        "@type": "Organization",
        name: "Wealthyhood",
        logo: {
          "@type": "ImageObject",
          url: "https://wealthyhood.com/img/logo-icon.png",
        },
      },
      mainEntityOfPage: canonicalUrl,
      articleSection: category,
    }

    if (wordCount) {
      schema.wordCount = wordCount
    }

    if (faqEntities.length > 0) {
      schema.mainEntity = {
        "@type": "FAQPage",
        mainEntity: faqEntities,
      }
    }

    if (howToSteps.length > 0) {
      schema.hasPart = {
        "@type": "HowTo",
        name: howTo.title,
        step: howToSteps,
      }
    }

    return schema
  }

  render() {
    const postPageData = this.props.pageContext
    const locale = postPageData.locale
    const responseDocumentContent = JSON.parse(postPageData.content.raw)
    const responseDocumentReferences = postPageData.content.references
    const category = {
      name: postPageData.category.name,
      color: postPageData.category.color,
      slug: postPageData.category.slug,
    }
    const contentHTML = this.#transformRichTextToHTML(
      responseDocumentContent,
      responseDocumentReferences,
      locale
    )
    const tableOfContentItems = this.#getHeadersFromNodes(
      responseDocumentContent.content
    )
    const author = {
      name: postPageData.author.name,
      description: postPageData.author.shortDescription,
      image: postPageData.author.image,
      mail: postPageData.author.mail,
      linkedin: postPageData.author.linkedin,
      reddit: postPageData.author.reddit,
      medium: postPageData.author.medium,
      twitter: postPageData.author.twitter,
      slug: postPageData.author.slug,
    }
    const {
      metaTitle,
      metaImage,
      metaDescription,
      title,
      date,
      slug,
      relevantPosts,
      createdAt,
      updatedAt,
      schemaFaqs,
      schemaSteps,
      wordCount,
      hrefLangs,
    } = postPageData

    const schemaOrgMarkup = this.#generateSchemaOrgMarkup(
      title,
      `https${metaImage.file.url}`,
      author.name,
      author.slug,
      createdAt,
      updatedAt,
      metaDescription,
      `www.wealthyhood.com/${locale}/blog/${category.slug}/${slug}`.toLowerCase(),
      category.name,
      wordCount,
      schemaFaqs,
      schemaSteps,
      locale
    )
    return (
      <Layout
        activePage={"post"}
        backgroundColor={"#fff"}
        headerBackgroundColor={"#F6F8FD"}
      >
        <Seo
          description={metaDescription}
          title={metaTitle}
          canonicalUrlPath={`/${locale}/blog/${category.slug}/${slug}`.toLowerCase()}
          image={metaImage.file.url}
          hrefLangs={hrefLangs}
          isDynamicImage={true}
          schemaMarkup={schemaOrgMarkup}
          lang={locale === LocaleEnum.UK ? "en" : "el"}
        />
        <div className="blog-font-family m-0 p-0">
          {/*Blog Post Header*/}
          <div
            style={{
              backgroundColor: "#F6F8FD",
            }}
          >
            <div className="container space-top-2 space-bottom-1 d-flex flex-column gap-4">
              <div className="d-flex gap-4">
                <div>
                  <Link className="blog-breadcrump blog-text-dark" to={`/`}>
                    <span>Wealthyhood</span>
                  </Link>{" "}
                  /{" "}
                  <Link
                    className="blog-breadcrump blog-text-dark"
                    to={`/${locale}/blog/`}
                  >
                    <span>Blog</span>
                  </Link>{" "}
                  /{" "}
                  <Link
                    className="blog-breadcrump blog-text-dark"
                    to={`/${locale}/blog/${category.slug}/`}
                  >
                    <span>{category.name}</span>
                  </Link>
                </div>
                <span className="d-none d-lg-block blog-text-dark fw-bold">
                  {formatDateToLongFormWithoutComma(date, locale)}
                </span>
              </div>
              <h1 className="blog-header-title blog-text-dark m-0">{title}</h1>
              <BlogAuthorPreview
                date={date}
                image={author.image.file.url}
                name={author.name}
                slug={author.slug}
                locale={locale}
                className="d-block d-lg-none blog-text-dark"
              />
            </div>
          </div>

          {/*Blog Post Content*/}
          <div className="container blog-post-content-container space-top-2 space-bottom-2 mb-4 px-lg-0">
            <div className="d-flex">
              <div
                className="d-none d-lg-block blog-post-table-of-contents-container"
                // Make this sticky at top of the screen when user scrolls
              >
                <TableOfContents items={tableOfContentItems} locale={locale} />
              </div>
              <div
                className="col-12 px-xl-7 blog-post-html-container"
                dangerouslySetInnerHTML={{
                  __html: contentHTML,
                }}
              />
              <div
                className="d-none d-lg-block blog-post-author-container"
                // Make this sticky at top of the screen when user scrolls
              >
                <div className="stick-element-to-top">
                  <BlogAuthor
                    alignment={"vertical"}
                    image={author.image.file.url}
                    name={author.name}
                    linkedin={author.linkedin}
                    twitter={author.twitter}
                    reddit={author.reddit}
                    medium={author.medium}
                    slug={author.slug}
                    mail={author.mail}
                    description={author.description}
                    locale={locale}
                  />
                  <div className="d-none d-xl-block">
                    {locale === LocaleEnum.UK && (
                      <div className="mt-5 p-4 d-flex flex-column blog-post-form gap-4">
                        <h3 className="text-white fw-bold m-0 blog-post-form-title">
                          {translate(
                            DictionaryMappingEnum.BLOG_POST_FORM_TITLE,
                            locale
                          )}
                        </h3>
                        <p className="text-regular-no-font-family m-0 text-white d-xxl-only">
                          {translate(
                            DictionaryMappingEnum.BLOG_POST_FORM_SUBTITLE,
                            locale
                          )}
                        </p>
                        <DownloadAppBtn
                          locale={locale}
                          color="light"
                          className="button-short button-width-100"
                        />
                        <p className="text-small text-center m-0 text-white">
                          {translate(
                            DictionaryMappingEnum.CAPITAL_AT_RISK,
                            locale
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className="container space-2 my-3">
            <EmailCta locale={locale} />
          </div>

          {/*  Relevant post */}
          {relevantPosts?.length && (
            <div className="container space-bottom-2 mb-4 editors-pick-container d-flex flex-column">
              <h2 className="text-big blog-text-dark fw-bolder m-0">
                {translate(DictionaryMappingEnum.RELEVANT_POSTS, locale)}
              </h2>

              {/* Pass relevant posts */}
              <BlogMultiplePostPreview
                items={relevantPosts}
                itemsPerRow={3}
                locale={locale}
              />
              <div className="text-center">
                <Link
                  to={`/${locale}/blog/directory`}
                  className="cta cta-button btn button-medium text-medium-no-font-family btn-primary bg-dark-blue"
                >
                  {translate(DictionaryMappingEnum.VIEW_ALL_POSTS, locale)}
                </Link>
              </div>
            </div>
          )}
          <BlogPostMobileCTA locale={locale} />
        </div>
      </Layout>
    )
  }
}

export default withPathname(BlogPostTemplate)

export function Head() {
  return (
    <>
      <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"
        rel="stylesheet"
      />
    </>
  )
}
