import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import withPathname from "../hoc/withPathname"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { GatsbyImage, getImage, getSrc } from "gatsby-plugin-image"

class PressReleasePageTemplate extends React.Component {
  render() {
    const { title, subtitle, html, heroImage } = this.props.pageContext
    const image = heroImage && getImage(heroImage)
    const imageUrl = heroImage && getSrc(heroImage)

    return (
      <Layout activePage={"pressReleasePage"} backgroundColor={"#fff"}>
        <Seo description={subtitle} title={title} image={imageUrl} />
        <div className="noto-sans m-0 p-0 press-release-page">
          <div className="container px-4 space-top-1 space-top-lg-2 space-bottom-2">
            <div
              className="mx-auto space-bottom-1"
              style={{ maxWidth: "56rem" }}
            >
              <h2
                className="text-dark-blue"
                style={{ fontWeight: 800, marginBottom: "0.75rem" }}
              >
                {title}
              </h2>
              {subtitle && (
                <p
                  className="text-muted"
                  style={{
                    fontSize: "20px",
                    color: "#757575",
                    fontWeight: 400,
                    marginBottom: "1rem",
                  }}
                >
                  {subtitle}
                </p>
              )}
            </div>
            {image && (
              <div
                style={{
                  maxWidth: "56rem",
                  marginLeft: "auto",
                  marginRight: "auto",
                  marginBottom: "1rem",
                }}
              >
                <GatsbyImage
                  image={image}
                  alt={title}
                  style={{
                    borderRadius: "0.5rem",
                    boxShadow:
                      "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                  }}
                />
              </div>
            )}
          </div>
          <div className="container space-top-0 space-top-lg-1 space-bottom-2 mb-4 px-lg-4">
            <div
              className="noto-sans legal-content press-release-content"
              style={{
                maxWidth: "56rem",
                marginLeft: "auto",
                marginRight: "auto",
                fontSize: "1rem",
              }}
              dangerouslySetInnerHTML={{ __html: html }}
            />
          </div>
        </div>
      </Layout>
    )
  }
}

export default withPathname(withLanguageRegion(PressReleasePageTemplate))
