import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import withLanguageRegion from "../hoc/withLanguageRegion"
import withPathname from "../hoc/withPathname"
import { DictionaryEnum } from "../translations/dictionary-entries"
import { GatsbyImage, getImage } from "gatsby-plugin-image"

class PressReleasesDirectoryPageTemplate extends React.Component {
  render() {
    const { pageContext, translate } = this.props

    const pageTitle = translate(DictionaryEnum.PRESS_RELEASES_PAGE_TITLE)
    const metaTitle = translate(DictionaryEnum.PRESS_RELEASES_META_TITLE)
    const metaDescription = translate(
      DictionaryEnum.PRESS_RELEASES_META_DESCRIPTION
    )

    return (
      <Layout activePage={"press-releases"} backgroundColor={"#fff"}>
        <Seo description={metaDescription} title={metaTitle} />
        <div className="noto-sans m-0 p-0">
          {/* <!-- Title Section --> */}
          <div className="container space-top-2 space-bottom-lg-1">
            <div className="d-flex flex-column mb-0">
              <h1
                className="text-dark-blue tw-text-[48px] tw-font-bold space-top-lg-1 space-bottom-1"
                style={{ lineHeight: "120%" }}
              >
                {pageTitle}
              </h1>
            </div>
          </div>
          {/* <!-- End Title Section --> */}

          {/* <!-- Press Releases List Section --> */}
          <div className="container space-top-1 space-bottom-5 mb-4">
            {pageContext.pressReleases.length > 0 ? (
              <ul className="list-unstyled tw-pl-0">
                {pageContext.pressReleases.map((pressRelease, index) => {
                  const image = getImage(pressRelease.heroImage)

                  return (
                    <li
                      key={index}
                      className="tw-mb-4 tw-pb-4 tw-border-b tw-border-gray-200 last:tw-border-b-0"
                    >
                      <div
                        className={
                          "d-flex flex-row gap-4 align-items-center justify-content-center"
                        }
                      >
                        {image && (
                          <div
                            style={{
                              width: "336px",
                              marginRight: "auto",
                            }}
                          >
                            <GatsbyImage
                              image={image}
                              alt={""}
                              style={{
                                borderRadius: "0.5rem",
                                boxShadow:
                                  "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                              }}
                            />
                          </div>
                        )}
                        <div>
                          <a
                            href={`/${pageContext.language}/${pressRelease.path}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="tw-text-primary hover:tw-underline tw-flex tw-items-center mb-3"
                          >
                            <h4>{pressRelease.title}</h4>
                          </a>
                          <p className={"text-muted tw-text-[18px]"}>
                            {pressRelease.longSubtitle}
                          </p>
                        </div>
                      </div>
                    </li>
                  )
                })}
              </ul>
            ) : (
              <p className="text-center text-dark-gray">
                No press releases available at this time.
              </p>
            )}
          </div>
          {/* <!-- End Press Releases List Section --> */}
        </div>
      </Layout>
    )
  }
}

export default withPathname(
  withLanguageRegion(PressReleasesDirectoryPageTemplate)
)
