import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import financialStatementsData from "../configs/financial-reports.json"
import withLanguageRegion from "../hoc/withLanguageRegion"
import withPathname from "../hoc/withPathname"
import { DictionaryEnum } from "../translations/dictionary-entries"

class FinancialStatementsPage extends React.Component {
  /**
   * Construct filename: [Translated Prefix] [DD-MM-YYYY].pdf
   */
  #constructPdfPath = (statement) => {
    const { translate } = this.props
    const [year, month, day] = statement.date.split("-")
    const formattedDateForFilename = `${day}-${month}-${year}`

    return `${translate(
      DictionaryEnum.FILENAME_STATEMENT_PREFIX
    )} ${formattedDateForFilename}`
  }

  render() {
    const { pageContext, translate } = this.props

    // Sort statements by date (descending - most recent first)
    const sortedStatements = [...financialStatementsData].sort(
      (a, b) => new Date(b.date) - new Date(a.date)
    )

    const pageTitle = translate(DictionaryEnum.FINANCIAL_REPORTS_PAGE_TITLE)
    const metaTitle = translate(DictionaryEnum.FINANCIAL_REPORTS_META_TITLE)
    const metaDescription = translate(
      DictionaryEnum.FINANCIAL_REPORTS_META_DESCRIPTION
    )

    return (
      <Layout activePage={"financial-statements"} backgroundColor={"#fff"}>
        <Seo description={metaDescription} title={metaTitle} />
        <div className="noto-sans m-0 p-0">
          {/* <!-- Title Section --> */}
          <div className="container space-top-2 space-bottom-lg-1">
            <div className="d-flex flex-column mb-0">
              <h1
                className="text-dark-blue tw-text-[48px] tw-font-bold space-top-lg-1 space-bottom-1"
                style={{ lineHeight: "120%" }}
              >
                {pageTitle}
              </h1>
            </div>
          </div>
          {/* <!-- End Title Section --> */}

          {/* <!-- Statements List Section --> */}
          <div className="container space-top-1 space-bottom-5 mb-4">
            {sortedStatements.length > 0 ? (
              <ul className="list-unstyled tw-pl-0">
                {sortedStatements.map((statement, index) => {
                  const pdfFilename = this.#constructPdfPath(statement)

                  return (
                    <li
                      key={index}
                      className="tw-mb-4 tw-pb-4 tw-border-b tw-border-gray-200 last:tw-border-b-0"
                    >
                      <div>
                        <a
                          href={`/statements/${statement.pdfUrl}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="tw-text-primary hover:tw-underline tw-flex tw-items-center tw-text-[20px]"
                        >
                          <span className="material-symbols-outlined tw-mr-2">
                            {" "}
                            picture_as_pdf
                          </span>
                          <span>{pdfFilename}</span>
                        </a>
                      </div>
                    </li>
                  )
                })}
              </ul>
            ) : (
              <p className="text-center text-dark-gray">
                No financial statements available at this time.
              </p>
            )}
          </div>
          {/* <!-- End Statements List Section --> */}
        </div>
      </Layout>
    )
  }
}

export default withPathname(withLanguageRegion(FinancialStatementsPage))
