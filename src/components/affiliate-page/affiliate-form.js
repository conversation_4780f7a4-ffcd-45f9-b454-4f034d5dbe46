import React from "react"

class AffiliateForm extends React.Component {
  render() {
    return (
      <div className="tw-bg-white lg:tw-rounded-3xl tw-p-8 tw-relative lg:tw-shadow-lg">
        {/* Background Blur Effect */}
        <div
          className="tw-absolute tw-inset-0 tw-bg-[#536AE31A] tw-backdrop-blur-sm tw-rounded-3xl tw-hidden lg:tw-block"
          style={{
            zIndex: -1,
            left: "-110px",
            height: "50%",
            top: "25%",
          }}
        />

        {/* Form Content */}
        <div className="tw-relative tw-z-10">
          <h2 className="lg:tw-text-2xl tw-font-bold tw-text-left tw-mb-4 noto-sans">
            Join the Wealthyhood mission!
          </h2>
          <p className="tw-text-left tw-text-neutralPrimary tw-mb-7 noto-sans">
            Fill in your information to become an affiliate, and we'll be in
            touch soon!
          </p>
          <form
            className="tw-flex tw-flex-col tw-gap-4"
            name={"affiliate-form"}
            method="POST"
            data-netlify="true"
          >
            <input type="hidden" name="form-name" value="affiliate-form" />
            <label
              htmlFor={"name"}
              className={
                "noto-sans tw-text-[#101327] tw-font-semibold tw-text-left"
              }
              style={{ marginBottom: "-7px" }}
            >
              Name
            </label>
            <input
              id={"name"}
              name={"Name"}
              type="text"
              placeholder="Write your full name"
              className="tw-bg-[#F4F4F4] tw-rounded-lg tw-py-3 tw-px-4 tw-text-neutralPrimary"
            />
            <label
              htmlFor={"email"}
              className={
                "noto-sans tw-text-[#101327] tw-font-semibold tw-text-left"
              }
              style={{ marginBottom: "-7px" }}
            >
              Email
            </label>
            <input
              id={"email"}
              name={"Email"}
              type="email"
              placeholder="Write your email"
              className="tw-bg-[#F4F4F4] tw-rounded-lg tw-py-3 tw-px-4 tw-text-neutralPrimary"
            />
            <button
              type="submit"
              className={`cta cta-button btn btn-primary bg-dark-blue noto-sans tw-mt-4`}
              style={{
                width: "100% !important",
              }}
            >
              Apply now!
            </button>
          </form>
        </div>
      </div>
    )
  }
}

export default AffiliateForm
