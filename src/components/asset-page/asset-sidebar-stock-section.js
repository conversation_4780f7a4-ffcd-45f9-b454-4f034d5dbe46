import React from "react"
import DownloadAppBtn from "../download-app-btn"
import Decimal from "decimal.js"
import { formatCurrency } from "../../utils/formatterUtil"

class AssetSidebarStockSection extends React.Component {
  getBaseUrl() {
    if (typeof window !== "undefined") {
      const { pathname, hash } = window.location
      return pathname
    }
    return ""
  }

  getLinksToDisplay(shoudDisplayAssetNews) {
    return [
      { label: "About", slug: "#about", display: true },
      { label: "Metrics", slug: "#metrics", display: true },
      { label: "Forecasts", slug: "#forecasts", display: true },
      {
        label: "Earnings performance",
        slug: "#earnings-performance",
        display: true,
      },
      { label: "Financial performance", slug: "#financials", display: true },
      { label: "Sentiment analysis", slug: "#sentiment", display: true },
      { label: "FAQs", slug: "#faq", display: true },
      { label: "News", slug: "#news", display: shoudDisplayAssetNews },
    ]
  }

  getActiveHash() {
    if (typeof window !== "undefined") {
      const { hash } = window.location
      return hash
    }
    return ""
  }

  render() {
    const {
      className,
      name,
      ticker,
      logoUrl,
      assetReturn,
      tradedPrice,
      tradedCurrency,
      locale,
      shouldDisplayAssetNewsLink,
    } = this.props

    const isReturnPositive = assetReturn > 0
    const baseUrl = this.getBaseUrl()
    const activeHash = this.getActiveHash()

    const sidebarLinks = this.getLinksToDisplay(shouldDisplayAssetNewsLink)

    return (
      <div
        className={`tw-py-5 tw-px-4 tw-rounded-2xl lg:tw-border-2 tw-bg-white lg:tw-border-solid lg:tw-border-gray ${
          className ?? ""
        }`}
      >
        <img
          className="tw-max-h-[50%] lg:tw-max-h-full tw-max-w-[50%] lg:tw-w-full mx-auto tw-mb-5 tw-rounded-xl"
          src={logoUrl}
        />
        <h3 className="tw-text-neutralPrimary tw-text-lg tw-font-semibold tw-mb-6 tw-text-center">
          {name} • {ticker}
        </h3>
        {/* Stock price */}
        {tradedPrice && (
          <div className="tw-mb-3 tw-flex tw-flex-col tw-gap-[6px]">
            <div className="tw-order-2 lg:tw-order-1 tw-text-sm tw-text-darkGray tw-text-center">
              {ticker} current price
            </div>
            <div className="tw-order-1 lg:tw-order-2 tw-border-[1px] tw-border-solid tw-border-gray tw-py-[6px] tw-px-2 tw-rounded-3xl tw-flex tw-justify-between tw-items-center">
              <span className="tw-pl-3 tw-text-primary tw-font-semibold tw-text-2xl">
                {formatCurrency(tradedPrice, tradedCurrency, locale)}
              </span>
              <span
                className={`${
                  assetReturn > 0
                    ? "tw-text-[#23846A] tw-bg-[#DCFAF1]"
                    : "tw-text-[#D63C3C] tw-bg-[#FFE6E6]"
                } tw-rounded-3xl tw-font-semibold tw-rounded-3x tw-px-3 tw-py-1`}
              >
                {isReturnPositive ? "+" : "-"}
                {Decimal.abs(assetReturn).toNumber().toLocaleString(locale, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
                %
              </span>
            </div>
          </div>
        )}
        {/* CTA */}
        <div className="tw-hidden lg:tw-block tw-mb-6">
          <DownloadAppBtn className="tw-rounded-3xl !tw-w-full noto-sans tw-text-base tw-bg-neutralPrimary tw-text-white  tw-font-semibold tw-cursor-pointer hover:tw-text-white hover:tw-bg-opacity-90">
            Buy {ticker}
          </DownloadAppBtn>
          <p className="tw-text-base tw-font-normal tw-text-center tw-mt-1 tw-text-darkGray">
            Capital at risk.
          </p>
        </div>
        {/* Menu */}
        <ul className="tw-hidden lg:tw-flex tw-flex-col  p-0 m-0">
          {sidebarLinks
            .filter((item) => item.display)
            .map((item, i) => (
              <li key={i}>
                <div
                  className={`tw-p-2 tw-rounded-lg ${
                    activeHash === item.slug ? "tw-bg-gray" : ""
                  }`}
                >
                  <a
                    className={`noto-sans !tw-font-normal tw-text-sm tw-!leading-[21px] tw-text-[#10132799] hover:!tw-text-[#101327] hover:!tw-no-underline hover:!tw-font-normal ${
                      activeHash === item.slug
                        ? "tw-text-[#101327] tw-bg-gray"
                        : ""
                    }`}
                    href={baseUrl + item.slug}
                  >
                    {item.label}
                  </a>
                </div>
              </li>
            ))}
        </ul>
      </div>
    )
  }
}

export default AssetSidebarStockSection
