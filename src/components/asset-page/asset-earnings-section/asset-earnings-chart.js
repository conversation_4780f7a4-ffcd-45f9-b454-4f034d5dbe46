import React, { Component } from "react"
import { Scatter } from "react-chartjs-2"
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  Too<PERSON><PERSON>,
  Legend,
} from "chart.js"
import { formatCurrency } from "../../../utils/formatterUtil"

ChartJS.register(LinearScale, PointElement, Tooltip, Legend)

class Eps<PERSON>hart extends Component {
  constructor(props) {
    super(props)

    this.state = {}
  }

  render() {
    const { className, data, currency } = this.props

    const labels = data.map(item => item.displayDate)
    const actualEPSData = data.map(item => item.epsActual)
    const estimateEPSData = data.map(item => item.epsEstimate)

    const dataToDisplay = {
      labels: labels,
      datasets: [
        {
          label: "EPS Actual",
          data: actualEPSData.map((eps, index) => ({ x: index, y: eps })),
          backgroundColor: "#31BA96",
          borderColor: "#31BA96",
          borderWidth: 1,
          pointRadius: 10,
          hoverRadius: 12,
          hoverBorderWidth: 1,
        },
        {
          label: "EPS Estimate",
          data: estimateEPSData.map((eps, index) => ({ x: index, y: eps })),
          backgroundColor: "rgba(201, 203, 207, 0)",
          borderColor: "#536AE3",
          borderWidth: 1,
          pointRadius: 10,
          hoverRadius: 12,
          hoverBorderWidth: 1,
          pointStyle: "circle",
        },
      ],
    }

    const commonOptions = {
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: function(context) {
              const datasetLabel = context.dataset.label
              const value = context.raw.y
              return `${datasetLabel}: $${value.toFixed(2)}`
            },
          },
        },
      },
      responsive: true,
      maintainAspectRatio: false,
    }

    const desktopOptions = {
      ...commonOptions,
      layout: {
        padding: {
          right: 50,
          bottom: 5,
          left: 10,
        },
      },
      scales: {
        x: {
          border: {
            display: false,
          },
          type: "linear",
          position: "bottom",
          ticks: {
            stepSize: 1,
            callback: function(value) {
              return labels[value]
            },
            padding: 40,
            font: {
              size: 16,
            },
            color: "#11152E",
            crossAlign: "far",
          },
          grid: {
            tickMarkLength: 30,
            color: "rgba(0, 0, 0, 0)",
            zeroLineColor: "rgba(0, 0, 0, 0)",
          },
        },
        y: {
          beginAtZero: true,
          border: {
            display: false,
          },
          ticks: {
            callback: function(value) {
              return formatCurrency(value, currency, "en", 0, 2)
            },
            font: {
              size: 16,
            },
            color: "#11152E",
            autoSkip: true,
            maxTicksLimit: 3,
            padding: 85,
            crossAlign: "far",
          },
          grid: {
            color: "rgba(241, 243, 253, 1)",
            offset: false,
            drawTicks: false,
            borderDash: [5, 5], // Set the dash style
            lineWidth: 2,
            zeroLineWidth: 0,
          },
        },
      },
    }

    const mobileOptions = {
      ...commonOptions,
      layout: {
        padding: {
          right: 10,
          bottom: 5,
          left: 10,
        },
      },
      scales: {
        x: {
          border: {
            display: false,
          },
          type: "linear",
          position: "bottom",
          ticks: {
            stepSize: 1,
            callback: function(value) {
              return labels[value]
            },
            padding: 40,
            font: {
              size: 16,
            },
            color: "#11152E",
            crossAlign: "far",
          },
          grid: {
            tickMarkLength: 30,
            color: "rgba(0, 0, 0, 0)",
            zeroLineColor: "rgba(0, 0, 0, 0)",
          },
        },
        y: {
          beginAtZero: true,
          border: {
            display: false,
          },
          ticks: {
            callback: function(value) {
              return formatCurrency(value, currency, "en", 0, 2)
            },
            font: {
              size: 16,
            },
            color: "#11152E",
            autoSkip: true,
            maxTicksLimit: 3,
            padding: 65,
            crossAlign: "far",
          },
          grid: {
            color: "rgba(241, 243, 253, 1)",
            offset: false,
            drawTicks: false,
            borderDash: [5, 5], // Set the dash style
            lineWidth: 2,
            zeroLineWidth: 0,
          },
        },
      },
    }

    return (
      <div className={`tw-pb-0 ${className ?? ""}`}>
        <div className="tw-hidden lg:tw-block tw-relative tw-h-[364px]">
          <Scatter data={dataToDisplay} options={desktopOptions} />
        </div>
        <div className="lg:tw-hidden tw-relative tw-h-[364px]">
          <Scatter data={dataToDisplay} options={mobileOptions} />
        </div>
      </div>
    )
  }
}

export default EpsChart
