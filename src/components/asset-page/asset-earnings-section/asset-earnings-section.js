import React from "react"
import { AssetPageInfoTooltip } from "../asset-page-info-tooltip"
import <PERSON><PERSON><PERSON><PERSON> from "./asset-earnings-chart"
import DisplayModeButtons, { DisplayModeEnum } from "../display-mode-buttons"

const TenorsEnum = {
  QUARTERLY: "Quarterly",
  ANNUAL: "Annual",
}

const earningsResultConfig = {
  beat: {
    textColor: "#23846A",
    backgroundColor: " #DCFAF1",
    label: "Beat",
  },
  matched: {
    textColor: "#23846A",
    backgroundColor: " #DCFAF1",
    label: "Matched",
  },
  missed: {
    textColor: "#D63C3C",
    backgroundColor: "#FFE6E6",
    label: "Missed",
  },
  current: {
    textColor: "#11152E",
    backgroundColor: "#F4F4F4 ",
    label: "Current",
  },
}

const TextWithInfoButton = ({ text, info }) => (
  <div className="tw-flex tw-items-center">
    <h6 className="tw-text-darkGray">{text}</h6>
    <div className="tw-hidden lg:tw-block">
      <AssetPageInfoTooltip info={info} />
    </div>
  </div>
)

class AssetEarningsSection extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      activeTenor: TenorsEnum.QUARTERLY,
      activeDisplayMode: DisplayModeEnum.TABLE,
    }
  }

  handleTabChange = (tenor) => {
    this.setState({ activeTenor: tenor })
  }

  handleDisplayModeChange = (displayMode) => {
    this.setState({ activeDisplayMode: displayMode })
  }

  render() {
    const { className, earnings, title, currency } = this.props
    const { activeTenor, activeDisplayMode } = this.state

    const selectedTenorEarnings =
      activeTenor === TenorsEnum.QUARTERLY
        ? earnings.quarterly
        : earnings.annual

    const earningTableFormat = selectedTenorEarnings.table

    const lastRowHeightClass = "tw-h-[3.50rem]"

    return (
      <section
        id="earnings-performance"
        className={`noto-sans ${className ?? ""}`}
      >
        <div className="tw-flex tw-justify-between tw-mb-6">
          <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl">
            {title}
          </h5>
          <DisplayModeButtons
            activeDisplayMode={activeDisplayMode}
            handleDisplayModeChange={this.handleDisplayModeChange}
          />
        </div>

        <div
          className={`tw-overflow-x-auto tw-hide-scrollbar-in-small-screens ${
            activeDisplayMode === DisplayModeEnum.TABLE ? "" : "tw-hidden"
          }`}
        >
          <div className="tw-grid tw-grid-cols-[1.5fr_repeat(5,1fr)] tw-py-7 tw-text-neutralPrimary tw-min-w-[675px]">
            <div className="tw-flex tw-flex-col tw-gap-14 tw-text-base tw-justify-between tw-bg-white tw-sticky tw-left-0 tw-z-10 tw-pr-2 lg:tw-pr-4 tw-border-r tw-border-gray">
              <TextWithInfoButton
                info="Realized earnings per share"
                text="Actual"
              />
              <TextWithInfoButton
                info="Predicted earnings per share"
                text="Expected"
              />
              <TextWithInfoButton
                info="Difference between actual and expected EPS"
                text="Surprise"
              />
              <div className={`${lastRowHeightClass} tw-flex`}>
                <div className="tw-flex tw-p-1 tw-text-neutralPrimary tw-font-medium tw-text-xs lg:tw-text-sm !tw-leading-[14px] tw-rounded-full tw-border-[1px] tw-border-gray tw-mr-auto tw-mb-auto"></div>
              </div>
            </div>
            {earningTableFormat.map((selectedTenorEarning, index) => {
              let config
              if (index === earningTableFormat.length - 1) {
                config = earningsResultConfig.current
              } else if (
                selectedTenorEarning.surprisePercent &&
                selectedTenorEarning.surprisePercent.includes("-")
              )
                config = earningsResultConfig.missed
              else config = earningsResultConfig.beat

              return (
                <div
                  key={index}
                  className="tw-flex tw-flex-col tw-gap-14 tw-text-lg tw-font-semibold tw-justify-between tw-border-r tw-border-gray tw-items-center"
                >
                  <div style={{ color: config.textColor }}>
                    {selectedTenorEarning.epsActual ?? "-"}
                  </div>
                  <div className="tw-text-neutralPrimary">
                    {selectedTenorEarning.epsEstimate ?? "-"}
                  </div>
                  <div style={{ color: config.textColor }}>
                    {selectedTenorEarning.surprisePercent ?? "-"}
                  </div>
                  <div
                    className={`${lastRowHeightClass} tw-flex tw-flex-col tw-items-center tw-content-center tw-text-sm`}
                  >
                    <span
                      className="tw-rounded-full tw-px-2 tw-py-[6px]"
                      style={{ background: config.backgroundColor }}
                    >
                      {selectedTenorEarning.displayDate}
                    </span>
                    <span
                      className="tw-font-normal"
                      style={{ color: config.textColor }}
                    >
                      {config.label}
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        <div className="tw-relative">
          <div className="tw-overflow-x-auto tw-hide-scrollbar-in-small-screens">
            <EpsChart
              className={`tw-relative tw-min-w-[500px] ${
                activeDisplayMode === DisplayModeEnum.CHART &&
                activeTenor === TenorsEnum.QUARTERLY
                  ? ""
                  : "tw-hidden"
              }`}
              data={earnings.quarterly.chart}
              currency={currency}
            />
            <EpsChart
              className={`tw-relative tw-min-w-[500px] ${
                activeDisplayMode === DisplayModeEnum.CHART &&
                activeTenor === TenorsEnum.ANNUAL
                  ? ""
                  : "tw-hidden"
              }`}
              data={earnings.annual.chart}
              currency={currency}
            />
          </div>
          <div className="tw-absolute tw-z-10 tw-bottom-[48px] tw-bg-white">
            <div className="tw-flex tw-p-1 tw-text-neutralPrimary tw-font-medium tw-text-xs lg:tw-text-sm !tw-leading-[14px] tw-rounded-full tw-border-[1px] tw-border-gray tw-mr-auto tw-mb-auto">
              <span
                className={`tw-px-[6px] lg:tw-px-[10px] tw-py-[6px] cursor-pointer tw-rounded-3xl ${
                  activeTenor === TenorsEnum.QUARTERLY ? "tw-bg-gray " : ""
                }`}
                onClick={() => this.handleTabChange(TenorsEnum.QUARTERLY)}
              >
                {TenorsEnum.QUARTERLY}
              </span>
              <span
                className={`tw-px-[6px] lg:tw-px-[10px] tw-py-[6px] cursor-pointer tw-rounded-3xl ${
                  activeTenor === TenorsEnum.ANNUAL ? "tw-bg-gray " : ""
                }`}
                onClick={() => this.handleTabChange(TenorsEnum.ANNUAL)}
              >
                {TenorsEnum.ANNUAL}
              </span>
            </div>
          </div>
        </div>
      </section>
    )
  }
}

export default AssetEarningsSection
