import React from "react"
import Markdown from "marked-react"
import AssetFaqCustomAccordion from "./asset-faq-custom-accordion"

const generateStockFaqs = (assetData) => {
  const faqs = [
    {
      title: `How much is a ${assetData.name} share?`,
      answer: `${assetData.name} shares are currently traded for ${assetData.tradedPrice} per share.`,
    },
    {
      title: `How many shares does ${assetData.name} have?`,
      answer: `${assetData.name} currently has ${assetData.outstandingShares} shares.`,
    },
    {
      title: `Does ${assetData.name} pay dividends?`,
      answer: `${assetData.dividend ? "Yes" : "No"}, ${assetData.name} ${
        assetData.dividend ? "does" : "doesn't"
      } pay dividends.`,
    },
    {
      title: `What is  ${assetData.name} 52 week high?`,
      answer: `${assetData.name} 52 week high is ${assetData.week52High}.`,
    },
    {
      title: `What is ${assetData.name} 52 week low?`,
      answer: `${assetData.name} 52 week low is ${assetData.week52Low}.`,
    },
    {
      title: `What is the 200-day moving average of ${assetData.name}?`,
      answer: `${assetData.name} 200-day moving average is ${assetData.day200Ma}.`,
    },
    {
      title: `Who is ${assetData.name} CEO?`,
      answer: `The CEO of ${assetData.name} is ${assetData.ceo}.`,
    },
    {
      title: `How many employees ${assetData.name} has?`,
      answer: `${assetData.name} has ${assetData.employees} employees.`,
    },
    {
      title: `What is the market cap of ${assetData.name}?`,
      answer: `The market cap of ${assetData.name}  is ${assetData.marketCap}.`,
    },
    {
      title: `What is the P/E of ${assetData.name}?`,
      answer: `The current P/E of ${assetData.name}  is ${assetData.peRatio}.`,
    },
    {
      title: `What is the EPS of ${assetData.name}?`,
      answer: `The EPS of ${assetData.name}  is ${assetData.eps}.`,
    },
    {
      title: `What is the PEG Ratio of ${assetData.name}?`,
      answer: `The PEG Ratio of ${assetData.name} is ${assetData.pegRatio}.`,
    },
  ]

  if (assetData.analystRating !== null) {
    faqs.push({
      title: `What do analysts say about ${assetData.name}?`,
      answer: `According to the analysts ${assetData.name} is considered a ${assetData.analystRating}.`,
    })
  }

  return faqs
}

class AssetFAQStockSection extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      expandedAccordionKey: "",
    }
  }

  _onCustomAccordionClick(cardKey) {
    this.setState((prevState) => ({
      expandedAccordionKey:
        prevState.expandedAccordionKey !== cardKey ? cardKey : "",
    }))
  }

  render() {
    const { className, assetData } = this.props
    const { expandedAccordionKey } = this.state

    const faqs = generateStockFaqs(assetData)
    return (
      <div
        id="faq"
        className={`tw-bg-gray-200 lg:tw-bg-white ${className ?? ""}`}
      >
        <h3 className="!tw-text-[2.5rem] !tw-leading-[120%] tw-font-bold tw-mb-10 tw-text-center">
          FAQs
        </h3>
        <div className="tw-mt-6 lg:tw-mt-8 ">
          <div className="tw-flex tw-flex-col tw-gap-6 lg:tw-gap-8">
            {faqs.map((faq, index) => (
              <AssetFaqCustomAccordion
                id={`faq-card-${index}`}
                key={`faq-card-${index}`}
                title={faq.title}
                className=""
                expand={expandedAccordionKey === `faq-card-${index}`}
                onClick={() =>
                  this._onCustomAccordionClick(`faq-card-${index}`)
                }
              >
                <div className="tw-text-base tw-text-neutralPrimary noto-sans faq-content">
                  <Markdown gfm breaks value={faq.answer} />
                </div>
              </AssetFaqCustomAccordion>
            ))}
          </div>
        </div>
      </div>
    )
  }
}

export default AssetFAQStockSection
