import React from "react"
import { AssetPageInfoTooltip } from "../asset-page-info-tooltip"

const MetricsAdvancedInfoItem = ({ label, value, info }) => (
  <div className="tw-flex tw-justify-between tw-items-center">
    <div className="tw-flex tw-items-center">
      <h6 className="tw-text-darkGray">{label}</h6>
      <div className="tw-hidden lg:tw-block">
        <AssetPageInfoTooltip info={info} />
      </div>
    </div>
    <div className="tw-text-neutralPrimary tw-font-semibold">
      {value || value === 0 ? value : "-"}
    </div>
  </div>
)

class AssetMetricsEtfAdvanced extends React.Component {
  render() {
    const { className, metrics } = this.props
    const { valuation, growth, price, technicals } = metrics

    return (
      <div
        className={`tw-grid tw-grid-cols-1 tw-gap-6 lg:tw-grid-cols-2  ${
          className ?? ""
        }`}
      >
        {/* Valuation */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Valuation</h5>
          <MetricsAdvancedInfoItem
            info="Current price vs. expected future earnings"
            label="Price to prospective earnings"
            value={valuation.priceToProspectiveEarnings}
          />
          <MetricsAdvancedInfoItem
            info="Current price vs. net asset value"
            label="Price to book"
            value={valuation.priceToBook}
          />
          <MetricsAdvancedInfoItem
            info="Price compared to revenue per share"
            label="Price to sales"
            value={valuation.priceToSales}
          />
          <MetricsAdvancedInfoItem
            info="Price compared to cash flow per share"
            label="Price to cashflow"
            value={valuation.priceToCashflow}
          />
        </div>
        {/* End of Valuation */}
        {/* Price */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Price</h5>
          <MetricsAdvancedInfoItem
            info="The highest price at which the asset traded during the past year"
            label="52-week High"
            value={price.week52High}
          />
          <MetricsAdvancedInfoItem
            info="The lowest price at which the asset traded during the past year"
            label="52-week Low"
            value={price.week52Low}
          />
          <MetricsAdvancedInfoItem
            info="The average closing price of the asset over the past 50 days"
            label="50-day moving average"
            value={price.day50Ma}
          />
          <MetricsAdvancedInfoItem
            info="The average closing price of the asset over the past 200 days"
            label="200-day moving average"
            value={price.day200Ma}
          />
        </div>
        {/* End Of Price */}
        {/* Technicals */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Technicals</h5>
          <MetricsAdvancedInfoItem
            info="Year-to-date return"
            label="YTD"
            value={technicals.ytd}
          />
          <MetricsAdvancedInfoItem
            info="Return over the last year"
            label="Year 1"
            value={technicals.year1}
          />
          <MetricsAdvancedInfoItem
            info="Return over the last three years"
            label="Year 3"
            value={technicals.year3}
          />
          <MetricsAdvancedInfoItem
            info="Return over the last five years"
            label="Year 5"
            value={technicals.year5}
          />
          <MetricsAdvancedInfoItem
            info="Return over the last ten years"
            label="Year 10"
            value={technicals.year10}
          />
          <MetricsAdvancedInfoItem
            info="Price fluctuation in the last year"
            label="Volatility Year 1"
            value={technicals.volatilityYear1}
          />
          <MetricsAdvancedInfoItem
            info="Price fluctuation over the last three years"
            label="Volatility Year 3"
            value={technicals.volatilityYear3}
          />
          <MetricsAdvancedInfoItem
            info="Predicted return over the next three years"
            label="Expected Return Year 3"
            value={technicals.expectedReturnYear3}
          />
          <MetricsAdvancedInfoItem
            info="Risk-adjusted return"
            label="Sharpe Ratio"
            value={technicals.sharpRatio}
          />
        </div>
        {/* End Of Technicals */}
        {/* Growth*/}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Growth </h5>
          <MetricsAdvancedInfoItem
            info="Expected future earnings"
            label="Long term projected earnings"
            value={growth.longTermProtectedEarnings}
          />
          <MetricsAdvancedInfoItem
            info="Past earnings performance"
            label="Historical earnings"
            value={growth.historicalEarnings}
          />
          <MetricsAdvancedInfoItem
            info="Revenue generated"
            label="Sales"
            value={growth.sales}
          />
          <MetricsAdvancedInfoItem
            info="Cash generated from operations"
            label="Cash flow"
            value={growth.cashflow}
          />
          <MetricsAdvancedInfoItem
            info="Net asset value"
            label="Book value"
            value={growth.bookValue}
          />
        </div>
        {/* End Of Growth*/}
      </div>
    )
  }
}

export default AssetMetricsEtfAdvanced
