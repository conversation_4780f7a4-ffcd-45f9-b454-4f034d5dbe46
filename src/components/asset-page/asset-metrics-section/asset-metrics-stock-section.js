import React from "react"
import AssetMetricsStockBasic from "./asset-metrics-stock-basic"
import AssetMetricsStockAdvanced from "./asset-metrics-stock-advanced"

const TabNames = {
  BASIC: "Basic",
  ADVANCED: "Advanced",
}

class AssetMetricsStockSection extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      activeTab: TabNames.ADVANCED,
    }
  }

  handleTabChange = (tab) => {
    this.setState({ activeTab: tab })
  }

  render() {
    const { className, assetMetrics } = this.props
    const { basics, advanced } = assetMetrics
    const { activeTab } = this.state

    return (
      <section id="metrics" className={`noto-sans ${className ?? ""}`}>
        <div className="tw-flex tw-justify-between tw-mb-6">
          <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl">
            Metrics
          </h5>
          <div className="tw-flex tw-text-neutralPrimary tw-font-semibold !tw-leading-[1.125rem]">
            <span
              className={`tw-px-4 tw-py-2 cursor-pointer tw-rounded-3xl ${
                activeTab === TabNames.BASIC
                  ? "tw-bg-neutralPrimary tw-text-white"
                  : ""
              }`}
              onClick={() => this.handleTabChange(TabNames.BASIC)}
            >
              {TabNames.BASIC}
            </span>
            <span
              className={`tw-px-4 tw-py-2 cursor-pointer tw-rounded-3xl ${
                activeTab === TabNames.ADVANCED
                  ? "tw-bg-neutralPrimary tw-text-white"
                  : ""
              }`}
              onClick={() => this.handleTabChange(TabNames.ADVANCED)}
            >
              {TabNames.ADVANCED}
            </span>
          </div>
        </div>

        <AssetMetricsStockBasic
          metrics={basics}
          className={` ${activeTab === TabNames.BASIC ? "" : "tw-hidden"}`}
        />
        <AssetMetricsStockAdvanced
          metrics={advanced}
          className={` ${activeTab === TabNames.ADVANCED ? "" : "tw-hidden"}`}
        />
      </section>
    )
  }
}

export default AssetMetricsStockSection
