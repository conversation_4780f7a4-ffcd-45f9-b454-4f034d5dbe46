import React from "react"
import { AssetPageInfoTooltip } from "../asset-page-info-tooltip"

const MetricsAdvancedInfoItem = ({ label, value, info }) => (
  <div className="tw-flex tw-justify-between tw-items-center">
    <div className="tw-flex tw-items-center">
      <h6 className="tw-text-darkGray">{label}</h6>
      <div className="tw-hidden lg:tw-block">
        <AssetPageInfoTooltip info={info} />
      </div>
    </div>
    <div className="tw-text-neutralPrimary tw-font-semibold">
      {value || value === 0 ? value : "-"}
    </div>
  </div>
)

class AssetMetricsStockAdvanced extends React.Component {
  render() {
    const { className, metrics } = this.props
    const {
      priceVolume,
      dividends,
      valuation,
      earnings,
      technicals,
      managementEffectiveness,
      growth,
      sharesStats,
    } = metrics

    return (
      <div
        className={`tw-grid tw-grid-cols-1 tw-gap-6 lg:tw-grid-cols-2  ${
          className ?? ""
        }`}
      >
        {/* Price & Volume */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Price & volume</h5>
          <MetricsAdvancedInfoItem
            info="Total market value of a company's shares"
            label="Market cap"
            value={priceVolume.marketCap}
          />
          <MetricsAdvancedInfoItem
            info="The average number of shares traded each day"
            label="Average daily volume"
            value={priceVolume.averageDailyVolume}
          />
          <MetricsAdvancedInfoItem
            info="The percentage change in the stock's price over the last 90 days"
            label="90-day return"
            value={priceVolume.return90days}
          />
          <MetricsAdvancedInfoItem
            info="The percentage change in the stock's price over the last 30 days"
            label="30-day return"
            value={priceVolume.return30days}
          />
          <MetricsAdvancedInfoItem
            info="The percentage change in the stock's price over the last 7 days"
            label="7-day return"
            value={priceVolume.return7days}
          />
        </div>
        {/* End of Price & Volume */}
        {/* Profit Dividends */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Dividends</h5>
          <MetricsAdvancedInfoItem
            info="The amount of money paid to shareholders for each share they own"
            label="Dividend per share"
            value={dividends.dividendPerShare}
          />
          <MetricsAdvancedInfoItem
            info="The percentage of a company's share price that is paid out in dividends each year"
            label="Dividend yield"
            value={dividends.dividendYield}
          />
          <MetricsAdvancedInfoItem
            info="The expected dividend payment per share for the next year"
            label="Forward dividend per share"
            value={dividends.forwardDividendPerShare}
          />
          <MetricsAdvancedInfoItem
            info="Expected annual dividend as a percentage of the current stock price"
            label="Forward dividend yield"
            value={dividends.forwardDividendYield}
          />
          <MetricsAdvancedInfoItem
            info="The proportion of earnings paid out as dividends to shareholders"
            label="Payout ratio"
            value={dividends.payoutRatio}
          />
        </div>
        {/* End Of Margins */}
        {/* Valuation */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Valuation</h5>
          <MetricsAdvancedInfoItem
            info="Price-to-Earnings ratio, showing how much investors are willing to pay per dollar of earnings"
            label="P/E ratio"
            value={valuation.peRatio}
          />
          <MetricsAdvancedInfoItem
            info="Estimated Price-to-Earnings ratio based on expected future earnings"
            label="Forward P/E"
            value={valuation.forwardPe}
          />
          <MetricsAdvancedInfoItem
            info="Price/Earnings to Growth ratio, comparing a company's P/E ratio to its earnings growth rate"
            label="PEG ratio"
            value={valuation.pegRatio}
          />
          <MetricsAdvancedInfoItem
            info="P/E ratio calculated using the last 12 months of earnings"
            label="Trailing P/E"
            value={valuation.trailingPe}
          />
          <MetricsAdvancedInfoItem
            info="The ratio of a company's stock price to its revenues"
            label="Price to sales"
            value={valuation.priceToSales}
          />
          <MetricsAdvancedInfoItem
            info="The ratio of a company's stock price to its book value per share"
            label="Price to book"
            value={valuation.priceToBook}
          />
        </div>
        {/* End Of Valuation */}
        {/* Earnings */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Earnings</h5>
          <MetricsAdvancedInfoItem
            info="Earnings per share, the portion of a company's profit allocated to each outstanding share"
            label="EPS"
            value={earnings.eps}
          />
          <MetricsAdvancedInfoItem
            info="Analysts' prediction of EPS for the current quarter"
            label="EPS estimate (current quarter) "
            value={earnings.epsEstimateCurrent}
          />
          <MetricsAdvancedInfoItem
            info="Analysts' prediction of EPS for the next quarter"
            label="EPS estimate (next quarter)"
            value={earnings.epsEstimateNext}
          />
          <MetricsAdvancedInfoItem
            info="Earnings before interest, taxes, depreciation, and amortization"
            label="EBITDA"
            value={earnings.ebitda}
          />
          <MetricsAdvancedInfoItem
            info="Total revenue over the trailing twelve months"
            label="Revenues (TTM)"
            value={earnings.revenueTtm}
          />
          <MetricsAdvancedInfoItem
            info="Total revenue per share over the trailing twelve months"
            label="Revenues per share (TTM)"
            value={earnings.revenuePerShareTtm}
          />
        </div>
        {/* End Of Earnings */}
        {/* Technicals */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Technicals</h5>
          <MetricsAdvancedInfoItem
            info="A measure of a stock's volatility compared to the market"
            label="Beta"
            value={technicals.beta}
          />
          <MetricsAdvancedInfoItem
            info="The highest price at which the stock traded during the past year"
            label="52-week High"
            value={technicals.week52High}
          />
          <MetricsAdvancedInfoItem
            info="The lowest price at which the stock traded during the past year"
            label="52-week Low"
            value={technicals.week52Low}
          />
          <MetricsAdvancedInfoItem
            info="The average closing price of the stock over the past 50 days"
            label="50-day moving average"
            value={technicals.day50Ma}
          />
          <MetricsAdvancedInfoItem
            info="The average closing price of the stock over the past 200 days"
            label="200-day moving average"
            value={technicals.day200Ma}
          />
          <MetricsAdvancedInfoItem
            info="The ratio of shares currently shorted to average daily shares traded"
            label="Short ratio"
            value={technicals.shortRatio}
          />
          <MetricsAdvancedInfoItem
            info="Percentage of total shares that are currently sold short"
            label="Short %"
            value={technicals.shortPercent}
          />
        </div>
        {/* End Of Technicals */}
        {/* Management Effectiveness */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">
            Management effectiveness
          </h5>
          <MetricsAdvancedInfoItem
            info="Return on Equity, a measure of financial efficiency calculated by dividing net income by shareholder equity"
            label="ROE (TTM)"
            value={managementEffectiveness.roeTtm}
          />
          <MetricsAdvancedInfoItem
            info="Return on Assets, a measure of how effectively a company uses its assets to generate earnings"
            label="ROA (TTM)"
            value={managementEffectiveness.roaTtm}
          />
          <MetricsAdvancedInfoItem
            info="The percentage of revenue that has turned into profit"
            label="Profit margin"
            value={managementEffectiveness.profitMargin}
          />
          <MetricsAdvancedInfoItem
            info="The percentage of revenue that exceeds the cost of goods sold"
            label="Gross profit margin"
            value={managementEffectiveness.grossProfit}
          />
          <MetricsAdvancedInfoItem
            info="The percentage of revenue left after subtracting operating expenses"
            label="Operating margin"
            value={managementEffectiveness.operatingMargin}
          />
        </div>
        {/* End Of Management Effectiveness */}
        {/* Growth*/}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Growth </h5>
          <MetricsAdvancedInfoItem
            info="The percentage increase in earnings compared to the same quarter in the previous year"
            label="Quarterly earnings growth (YoY) "
            value={growth.quarterlyEarningsGrowthYoy}
          />
          <MetricsAdvancedInfoItem
            info="The percentage increase in revenue compared to the same quarter in the previous year"
            label="Quarterly revenue growth (YoY)"
            value={growth.quarterlyRevenueGrowthYoy}
          />
        </div>
        {/* End Of Growth*/}
        {/* Shares Stats */}
        <div className="tw-rounded-2xl tw-p-8 tw-bg-gray tw-flex tw-flex-col tw-gap-6">
          <h5 className="!tw-text-lg !tw-font-semibold">Share stats</h5>
          <MetricsAdvancedInfoItem
            info="The total number of shares currently owned by all shareholders, including shares held by institutional investors and company insiders"
            label="Outstanding Shares"
            value={sharesStats.outstandingShares}
          />
          <MetricsAdvancedInfoItem
            info="The number of shares available for trading by the general public"
            label="Float"
            value={sharesStats.float}
          />
          <MetricsAdvancedInfoItem
            info="The percentage of shares that are owned by insiders (e.g., executives, directors) of the company"
            label="Insiders %"
            value={sharesStats.insiders}
          />
          <MetricsAdvancedInfoItem
            info="The percentage of shares that are owned by institutional investors such as mutual funds, pension funds, etc"
            label="Institutions %"
            value={sharesStats.institutions}
          />
        </div>
        {/* End Of Shares Stats */}
      </div>
    )
  }
}

export default AssetMetricsStockAdvanced
