import React from "react"
import { AssetPageInfoTooltip } from "../asset-page-info-tooltip"

const MetricsBasicInfoItem = ({ label, value, info }) => (
  <div className="tw-flex lg:tw-flex-col lg:tw-basis-1/5 tw-justify-between lg:tw-justify-start">
    <div className="tw-flex tw-items-center">
      <h6 className="tw-text-darkGray">{label}</h6>
      <div className="tw-hidden lg:tw-block">
        <AssetPageInfoTooltip info={info} />
      </div>
    </div>
    <div className="tw-text-neutralPrimary tw-font-semibold">
      {value || value === 0 ? value : "-"}
    </div>
  </div>
)

class AssetMetricsStockBasic extends React.Component {
  render() {
    const { className, metrics } = this.props
    const {
      marketCap,
      peRatio,
      eps,
      dividendYield,
      beta,
      forwardPeRatio,
      ebitda,
      exDividendDate,
    } = metrics

    return (
      <div
        className={`tw-rounded-[1.25rem] tw-p-8 lg:tw-p-6 tw-bg-gray tw-flex tw-flex-col tw-gap-6 ${
          className ?? ""
        }`}
      >
        <div className="tw-flex tw-flex-col lg:tw-flex-row tw-justify-between tw-gap-6 lg:tw-gap-0">
          <MetricsBasicInfoItem
            info="Total market value of a company's shares"
            label="Market cap"
            value={marketCap}
          />
          <MetricsBasicInfoItem
            info="Price-to-Earnings ratio; share price divided by earnings per share"
            label="P/E ratio"
            value={peRatio}
          />
          <MetricsBasicInfoItem
            info="Company's profit divided by the number of shares"
            label="EPS"
            value={eps}
          />
          <MetricsBasicInfoItem
            info="Dividend as a percentage of the stock price"
            label="Dividend Yield"
            value={dividendYield}
          />
        </div>
        <div className="tw-flex tw-flex-col lg:tw-flex-row tw-justify-between tw-gap-6 lg:tw-gap-0">
          <MetricsBasicInfoItem
            info="Stock's volatility compared to the market"
            label="Beta"
            value={beta}
          />
          <MetricsBasicInfoItem
            info="Price-to-earnings ratio based on expected future earnings"
            label="Forward P/E ratio"
            value={forwardPeRatio}
          />
          <MetricsBasicInfoItem
            info="Earnings Before Interest, Taxes, Depreciation, and Amortization"
            label="EBIDTA"
            value={ebitda}
          />
          <MetricsBasicInfoItem
            info="The cutoff date to be eligible for the next dividend"
            label="Ex dividend date"
            value={exDividendDate}
          />
        </div>
      </div>
    )
  }
}

export default AssetMetricsStockBasic
