import React from "react"
import { formatDateToRelative } from "../../../utils/date"

const SentimentIcon = ({ sentiment }) => {
  const iconDetails = {
    Positive: {
      icon: "thumb_up_alt",
      color: "#23846A",
      background: "#DCFAF1",
    },
    Negative: {
      icon: "thumb_down_alt",
      color: "#D63C3C",
      background: "#FFEFF4",
    },
    Neutral: {
      icon: "thumbs_up_down",
      color: "#D59A04",
      background: "#FFEDBF",
    },
  }

  const sentimentConfig = iconDetails[sentiment]

  if (!sentimentConfig) return null

  return (
    <div
      className="d-flex gap-2 align-items-center  tw-rounded-full tw-p-[0.375rem]"
      style={{
        background: sentimentConfig.background,
      }}
    >
      <span
        className="material-symbols-rounded !tw-text-[1.125rem] align-self-center"
        style={{
          color: sentimentConfig.color,
        }}
      >
        {sentimentConfig.icon}
      </span>
    </div>
  )
}

class AssetNewsItem extends React.Component {
  render() {
    const { newsItem, className } = this.props
    return (
      <div className={`noto-sans ${className ?? ""}`}>
        <a
          className="tw-flex tw-flex-col tw-cursor-pointer tw-gap-4 hover:!tw-no-underline"
          style={{ marginBottom: "20px", color: "#11152E" }}
          href={newsItem.news_url}
          target="_blank noreferrer"
        >
          <div className="tw-h-full tw-w-full">
            <img
              className="tw-max-w-full tw-max-h-full tw-rounded-[1.125rem]"
              src={newsItem.image_url}
            />
          </div>
          <div className="tw-flex tw-flex-col tw-justify-center tw-gap-4">
            <div className="tw-flex align-items-center tw-gap-2 !tw-font-normal tw-text-darkGray tw-text-sm">
              <SentimentIcon sentiment={newsItem.sentiment} />
              <span>•</span>
              <span>{newsItem.sentiment}</span>
            </div>
            <h5 className="!tw-font-semibold tw-text-neutralPrimary tw-text-2xl text-up-to-two-lines">
              {newsItem.title}
            </h5>
            <p className="tw-text-sm tw-text-darkGray !tw-font-normal text-up-to-three-lines">
              {newsItem.text}
            </p>
            <div className="tw-flex !tw-font-normal align-items-center tw-gap-2 tw-text-darkGray tw-text-sm">
              <span
                className="tw-text-neutralPrimary !tw-font-semibold"
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "50%",
                }}
              >
                {newsItem.source_name}
              </span>
              <span>•</span>
              <span>{formatDateToRelative(new Date(newsItem.date), true)}</span>
            </div>
          </div>
        </a>
      </div>
    )
  }
}

export default AssetNewsItem
