import React from "react"
import AssetNewsItem from "./asset-news-item"
import DownloadAppBtn from "../../download-app-btn"

class AssetNewsSection extends React.Component {
 

  render() {
    const { className, assetName, assetNews } = this.props

    return (
      <section id="news" className={`${className ?? ""}`}>
        <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl tw-mb-6">
          {assetName} news
        </h5>

        <div className="tw-flex tw-flex-col lg:tw-flex-row tw-gap-10">
          <AssetNewsItem newsItem={assetNews[0]} className="lg:tw-basis-1/3" />
          <AssetNewsItem newsItem={assetNews[1]} className="lg:tw-basis-1/3" />
          <AssetNewsItem newsItem={assetNews[2]} className="lg:tw-basis-1/3" />
        </div>
        <div className="tw-mt-7 lg:tw-mt-10">
          <DownloadAppBtn className="tw-flex tw-p-0 tw-border-0 tw-mx-auto">
            <span className="hover:tw-bg-opacity-90 tw-px-5 tw-py-3 tw-bg-neutralPrimary tw-text-white tw-rounded-3xl tw-text-base tw-font-semibold tw-cursor-pointer">
              View all news
            </span>
          </DownloadAppBtn>
        </div>
      </section>
    )
  }
}

export default AssetNewsSection
