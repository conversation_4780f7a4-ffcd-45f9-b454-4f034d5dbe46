import React from "react"

export const DisplayModeEnum = {
  CHART: "Chart",
  TABLE: "Table",
}

const DisplayModeButtons = ({
  activeDisplayMode,
  handleDisplayModeChange,
  className,
}) => (
  <div className={`${className ?? ""}`}>
    <div className="tw-flex tw-rounded-3xl tw-bg-gray tw-text-neutralPrimary tw-font-semibold tw-p-1">
      <span
        className={`!tw-text-lg cursor-pointer tw-rounded-full material-symbols-outlined tw-p-2 tw-h-[34px] !tw-flex tw-items-center ${
          activeDisplayMode === DisplayModeEnum.TABLE
            ? "tw-bg-primary tw-text-white"
            : ""
        }`}
        onClick={() => handleDisplayModeChange(DisplayModeEnum.TABLE)}
      >
        window
      </span>
      <span
        className={`!tw-text-lg cursor-pointer tw-rounded-full material-symbols-outlined tw-p-2 tw-h-[34px] !tw-flex tw-items-center ${
          activeDisplayMode === DisplayModeEnum.CHART
            ? "tw-bg-primary tw-text-white"
            : ""
        }`}
        onClick={() => handleDisplayModeChange(DisplayModeEnum.CHART)}
      >
        bar_chart_4_bars
      </span>
    </div>
  </div>
)

export default DisplayModeButtons
