import React, { Component } from "react"
import { Bar } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend,
} from "chart.js"
import { formatLargeNumber } from "../../../utils/formatterUtil"

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend)

class CashFlowsChart extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    const { className, data, currency } = this.props
    const labels = data.map((item) => item.displayDate)
    const operatingActivities = data.map((item) => item.operatingActivities)
    const investments = data.map((item) => item.investments)
    const financingActivities = data.map((item) => item.financingActivities)

    const dataToDisplayDesktop = {
      labels: labels,
      datasets: [
        {
          label: "Operating",
          data: operatingActivities,
          backgroundColor: "#536AE3",
          borderWidth: 0,
          barPercentage: 0.5,
          categoryPercentage: 0.35,
          minBarLength: 10,
        },
        {
          label: "Investing",
          data: investments,
          backgroundColor: "#23846A",
          borderWidth: 0,
          barPercentage: 0.5,
          categoryPercentage: 0.35,
          minBarLength: 10,
        },
        {
          label: "Financing",
          data: financingActivities,
          backgroundColor: "#333",
          borderWidth: 0,
          barPercentage: 0.5,
          categoryPercentage: 0.35,
          minBarLength: 10,
        },
      ],
    }

    const dataToDisplayMobile = {
      labels: labels,
      datasets: [
        {
          label: "Operating",
          data: operatingActivities,
          backgroundColor: "#536AE3",
          borderWidth: 0,
          barPercentage: 0.8,
          categoryPercentage: 0.3,
          minBarLength: 10,
        },
        {
          label: "Investing",
          data: investments,
          backgroundColor: "#23846A",
          borderWidth: 0,
          barPercentage: 0.8,
          categoryPercentage: 0.3,
          minBarLength: 10,
        },
        {
          label: "Financing",
          data: financingActivities,
          backgroundColor: "#333",
          borderWidth: 0,
          barPercentage: 0.8,
          categoryPercentage: 0.3,
          minBarLength: 10,
        },
      ],
    }

    const desktopOptions = {
      responsive: true,
      maintainAspectRatio: false,
      layout: {
        padding: {
          right: 30,
          bottom: 0,
          left: 10,
        },
      },
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: (context) => {
              const value = context.raw
              return `${context.dataset.label}: ${formatLargeNumber(
                value,
                currency,
                "en"
              )}`
            },
          },
        },
      },
      scales: {
        x: {
          border: {
            display: false,
          },
          ticks: {
            padding: 30,
            font: {
              size: 16,
            },
            color: "#11152E",
            crossAlign: "far",
          },
          grid: {
            tickMarkLength: 30,
            color: "rgba(0, 0, 0, 0)",
            zeroLineColor: "rgba(0, 0, 0, 0)",
          },
        },
        y: {
          border: {
            display: false,
          },
          beginAtZero: false,
          ticks: {
            callback: (value) => formatLargeNumber(value, currency, "en"),
            padding: 50,
            crossAlign: "far",
            font: {
              size: 16,
            },
            color: "#11152E",
            autoSkip: true,
            maxTicksLimit: 4,
          },
          grid: {
            color: "rgba(241, 243, 253, 1)",
            drawBorder: false,
            offsetGridLines: true,
            drawTicks: false,
            borderDash: [3, 4],
            lineWidth: 2,
            zeroLineWidth: 0,
          },
        },
      },
    }

    const mobileOptions = {
      responsive: true,
      maintainAspectRatio: false,
      layout: {
        padding: {
          right: 30,
          bottom: 0,
          left: 10,
        },
      },
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: (context) => {
              const value = context.raw
              return `${context.dataset.label}: ${formatLargeNumber(
                value,
                currency,
                "en"
              )}`
            },
          },
        },
      },
      scales: {
        x: {
          border: {
            display: false,
          },
          ticks: {
            padding: 30,
            font: {
              size: 16,
            },
            color: "#11152E",
            crossAlign: "far",
          },
          grid: {
            tickMarkLength: 30,
            color: "rgba(0, 0, 0, 0)",
            zeroLineColor: "rgba(0, 0, 0, 0)",
          },
        },
        y: {
          border: {
            display: false,
          },
          beginAtZero: false,
          ticks: {
            callback: (value) => formatLargeNumber(value, currency, "en"),
            padding: 30,
            crossAlign: "far",
            font: {
              size: 16,
            },
            color: "#11152E",
            autoSkip: true,
            maxTicksLimit: 4,
          },
          grid: {
            color: "rgba(241, 243, 253, 1)",
            drawBorder: false,
            offsetGridLines: true,
            drawTicks: false,
            borderDash: [3, 4],
            lineWidth: 2,
            zeroLineWidth: 0,
          },
        },
      },
    }

    return (
      <div className={`tw-pb-0 ${className ?? ""}`}>
        <div className="tw-hidden lg:tw-block tw-relative tw-h-[428px]">
          <Bar data={dataToDisplayDesktop} options={desktopOptions} />
        </div>
        <div className="lg:tw-hidden tw-relative tw-h-[428px]">
          <Bar data={dataToDisplayMobile} options={mobileOptions} />
        </div>
      </div>
    )
  }
}

export default CashFlowsChart
