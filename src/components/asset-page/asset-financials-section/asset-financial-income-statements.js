import React from "react"
import { AssetPageInfoTooltip } from "../asset-page-info-tooltip"
import { financialsResultConfig, TenorsEnum } from "./asset-financial-section"
import { DisplayModeEnum } from "../display-mode-buttons"
import AssetFinancialIncomeStatementChart from "./income-statements-chart"

const TextWithInfoButton = ({ text, info }) => (
  <div className="tw-flex tw-items-center">
    <h6 className="tw-text-darkGray">{text}</h6>
    <div className="tw-hidden lg:tw-block">
      <AssetPageInfoTooltip info={info} />
    </div>
  </div>
)
class AssetFinancialIncomeStatements extends React.Component {
  retrieveTextColor = (value) => {
    if (!value) return financialsResultConfig.neutral.textColor

    return value.includes("-")
      ? financialsResultConfig.negative.textColor
      : financialsResultConfig.positive.textColor
  }

  render() {
    const {
      className,
      incomeStatements,
      activeTenor,
      onTenorChangeHandler,
      activeDisplayMode,
      currency,
    } = this.props

    const tableData = incomeStatements.table
    const chartData = incomeStatements.chart

    const lastRowHeightClass = "tw-h-9"
    const gridClass =
      "tw-grid-cols-[1.1fr_repeat(3,1fr)] lg:tw-grid-cols-[1.3fr_repeat(4,1fr)]"

    return (
      <div className={`${className ?? ""}`}>
        <div
          className={`tw-overflow-x-auto tw-hide-scrollbar-in-small-screens ${
            activeDisplayMode === DisplayModeEnum.TABLE ? "" : "tw-hidden"
          }`}
        >
          <div
            className={`tw-grid ${gridClass} tw-py-7 tw-text-neutralPrimary tw-min-w-[510px]`}
          >
            <div className="tw-flex tw-flex-col tw-gap-14 tw-text-base tw-justify-between tw-bg-white tw-sticky tw-left-0 tw-z-10 tw-pr-2 lg:tw-pr-4 tw-border-r tw-border-gray">
              <TextWithInfoButton
                info="Total income from sales"
                text="Revenue"
              />
              <TextWithInfoButton
                info="Profit after all expenses"
                text="Net income"
              />
              <TextWithInfoButton
                info="Percentage of revenue that is profit"
                text="Profit margin"
              />
              <div className={`${lastRowHeightClass} tw-flex`}></div>
            </div>
            {activeDisplayMode === DisplayModeEnum.TABLE && (
              <div className="tw-hidden lg:tw-block"></div>
            )}
            {tableData.map((incomeStatement, index) => {
              let totalRevenueColor = ""
              let netIncomeColor = ""
              let profitMarginColor = ""

              if (index === tableData.length - 1) {
                totalRevenueColor = this.retrieveTextColor(
                  incomeStatement.freeCashFlow
                )

                netIncomeColor = this.retrieveTextColor(
                  incomeStatement.netIncome
                )

                profitMarginColor = this.retrieveTextColor(
                  incomeStatement.profitMargin
                )
              }

              return (
                <div
                  key={index}
                  className="tw-flex tw-flex-col tw-gap-14 tw-text-lg tw-font-semibold tw-justify-between tw-border-r tw-border-gray tw-items-center"
                >
                  <div
                    style={{
                      color: totalRevenueColor,
                    }}
                  >
                    {incomeStatement.totalRevenue}
                  </div>
                  <div
                    style={{
                      color: netIncomeColor,
                    }}
                  >
                    {incomeStatement.netIncome}
                  </div>
                  <div
                    style={{
                      color: profitMarginColor,
                    }}
                  >
                    {incomeStatement.profitMargin}
                  </div>
                  <div
                    className={`${lastRowHeightClass} tw-flex tw-flex-col tw-items-center tw-content-center tw-text-sm`}
                  >
                    <span
                      className="tw-rounded-full tw-px-2 tw-py-[6px]"
                      style={{
                        background:
                          financialsResultConfig.neutral.backgroundColor,
                      }}
                    >
                      {incomeStatement.displayDate}
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        <div className="tw-relative">
          <div className="tw-overflow-x-auto tw-hide-scrollbar-in-small-screens">
            <AssetFinancialIncomeStatementChart
              className={`tw-relative tw-min-w-[550px] ${
                activeDisplayMode === DisplayModeEnum.CHART ? "" : "tw-hidden"
              }`}
              data={chartData}
              currency={currency}
            />
          </div>
          <div className="tw-absolute tw-z-10 tw-bottom-[28px] tw-bg-white">
            <div className="tw-flex tw-p-1 tw-text-neutralPrimary tw-font-medium tw-text-xs lg:tw-text-sm !tw-leading-[14px] tw-rounded-full tw-border-[1px] tw-border-gray tw-mr-auto tw-mb-auto">
              <span
                className={`tw-px-[6px] lg:tw-px-[10px] tw-py-[6px] cursor-pointer tw-rounded-3xl ${
                  activeTenor === TenorsEnum.QUARTERLY ? "tw-bg-gray " : ""
                }`}
                onClick={() => onTenorChangeHandler(TenorsEnum.QUARTERLY)}
              >
                {TenorsEnum.QUARTERLY}
              </span>
              <span
                className={`tw-px-[6px] lg:tw-px-[10px] tw-py-[6px] cursor-pointer tw-rounded-3xl ${
                  activeTenor === TenorsEnum.ANNUAL ? "tw-bg-gray " : ""
                }`}
                onClick={() => onTenorChangeHandler(TenorsEnum.ANNUAL)}
              >
                {TenorsEnum.ANNUAL}
              </span>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default AssetFinancialIncomeStatements
