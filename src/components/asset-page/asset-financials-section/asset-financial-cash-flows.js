import React from "react"
import { AssetPageInfoTooltip } from "../asset-page-info-tooltip"
import { financialsResultConfig, TenorsEnum } from "./asset-financial-section"
import { DisplayModeEnum } from "../display-mode-buttons"
import CashFlowsChart from "./cashflows-chart"

const TextWithInfoButton = ({ text, info }) => (
  <div className="tw-flex tw-items-center">
    <h6 className="tw-text-darkGray">{text}</h6>
    <div className="tw-hidden lg:tw-block">
      <AssetPageInfoTooltip info={info} />
    </div>
  </div>
)

class AssetFinancialCashFlows extends React.Component {
  retrieveTextColor = (value) => {
    if (!value) return financialsResultConfig.neutral.textColor

    return value.includes("-")
      ? financialsResultConfig.negative.textColor
      : financialsResultConfig.positive.textColor
  }

  render() {
    const {
      className,
      cashFlows,
      activeTenor,
      activeDisplayMode,
      onTenorChangeHandler,
      currency,
    } = this.props

    const gridClass =
      "tw-grid-cols-[1.1fr_repeat(3,1fr)] lg:tw-grid-cols-[1.3fr_repeat(4,1fr)]"
    const tableData = cashFlows.table
    const chartData = cashFlows.chart

    const lastRowHeightClass = "tw-h-9"

    return (
      <div className={`${className ?? ""}`}>
        <div
          className={`tw-overflow-x-auto tw-hide-scrollbar-in-small-screens ${
            activeDisplayMode === DisplayModeEnum.TABLE ? "" : "tw-hidden"
          }`}
        >
          <div
            className={`tw-grid ${gridClass} tw-py-7 tw-text-neutralPrimary tw-min-w-[510px]`}
          >
            <div className="tw-flex tw-flex-col tw-gap-14 tw-text-base tw-justify-between tw-bg-white tw-sticky tw-left-0 tw-z-10 tw-pr-2 lg:tw-pr-4 tw-border-r tw-border-gray">
              <TextWithInfoButton
                info="Cash flow from business operations"
                text="Operating"
              />
              <TextWithInfoButton
                info="Cash flow from investments"
                text="Investing"
              />
              <TextWithInfoButton
                info="Cash flow from funding activities"
                text="Financing"
              />
              <TextWithInfoButton
                info="Cash available after expenses and investments"
                text="Free cash flow"
              />
              <div className={`${lastRowHeightClass} tw-flex`}></div>
            </div>
            {activeDisplayMode === DisplayModeEnum.TABLE && (
              <div className="tw-hidden lg:tw-block"></div>
            )}
            {tableData.map((cashFlow, index) => {
              let operatingActivitiesColor = ""
              let investmentsColor = ""
              let freeCashFlowColor = ""
              let financingActivitiesColor = ""

              if (index === tableData.length - 1) {
                operatingActivitiesColor = this.retrieveTextColor(
                  cashFlow.operatingActivities
                )

                investmentsColor = this.retrieveTextColor(cashFlow.investments)

                financingActivitiesColor = this.retrieveTextColor(
                  cashFlow.financingActivities
                )

                freeCashFlowColor = this.retrieveTextColor(
                  cashFlow.freeCashFlow
                )
              }

              return (
                <div
                  key={index}
                  className="tw-flex tw-flex-col tw-gap-14 tw-text-lg tw-font-semibold tw-justify-between tw-border-r tw-border-gray tw-items-center"
                >
                  <div style={{ color: operatingActivitiesColor }}>
                    {cashFlow.operatingActivities ?? "-"}
                  </div>
                  <div style={{ color: investmentsColor }}>
                    {cashFlow.investments ?? "-"}
                  </div>
                  <div style={{ color: financingActivitiesColor }}>
                    {cashFlow.financingActivities ?? "-"}
                  </div>
                  <div style={{ color: freeCashFlowColor }}>
                    {cashFlow.freeCashFlow ?? "-"}
                  </div>
                  <div
                    className={`${lastRowHeightClass} tw-flex tw-flex-col tw-items-center tw-content-center tw-text-sm`}
                  >
                    <span
                      className="tw-rounded-full tw-px-2 tw-py-[6px]"
                      style={{
                        background:
                          financialsResultConfig.neutral.backgroundColor,
                      }}
                    >
                      {cashFlow.displayDate}
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        <div className="tw-relative">
          <div className="tw-overflow-x-auto tw-hide-scrollbar-in-small-screens">
            <CashFlowsChart
              className={`tw-relative tw-min-w-[550px] ${
                activeDisplayMode === DisplayModeEnum.CHART ? "" : "tw-hidden"
              }`}
              data={chartData}
              currency={currency}
            />
          </div>
          <div className="tw-absolute tw-z-10 tw-bottom-[28px] tw-bg-white">
            <div className="tw-flex tw-p-1 tw-text-neutralPrimary tw-font-medium tw-text-xs lg:tw-text-sm !tw-leading-[14px] tw-rounded-full tw-border-[1px] tw-border-gray tw-mr-auto tw-mb-auto">
              <span
                className={`tw-px-[6px] lg:tw-px-[10px] tw-py-[6px] cursor-pointer tw-rounded-3xl ${
                  activeTenor === TenorsEnum.QUARTERLY ? "tw-bg-gray " : ""
                }`}
                onClick={() => onTenorChangeHandler(TenorsEnum.QUARTERLY)}
              >
                {TenorsEnum.QUARTERLY}
              </span>
              <span
                className={`tw-px-[6px] lg:tw-px-[10px] tw-py-[6px] cursor-pointer tw-rounded-3xl ${
                  activeTenor === TenorsEnum.ANNUAL ? "tw-bg-gray " : ""
                }`}
                onClick={() => onTenorChangeHandler(TenorsEnum.ANNUAL)}
              >
                {TenorsEnum.ANNUAL}
              </span>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default AssetFinancialCashFlows
