import React, { Component } from "react"
import { Bar } from "react-chartjs-2"
import { formatLargeNumber } from "../../../utils/formatterUtil"

class BalanceSheetsChart extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    const { className, data, currency } = this.props
    const labels = data.map(item => item.displayDate)
    const totalAssets = data.map(item => item.totalAssets)
    const totalLiabilities = data.map(item => item.totalLiabilities)

    const dataToDisplay = {
      labels: labels,
      datasets: [
        {
          label: "Total Assets",
          data: totalAssets,
          backgroundColor: "#536AE3",
          borderWidth: 0,
          barPercentage: 0.4,
          categoryPercentage: 0.3,
        },
        {
          label: "Total Liabilities",
          data: totalLiabilities,
          backgroundColor: "#F68181",
          borderColor: "rgba(255, 99, 132, 1)",
          borderWidth: 0,
          barPercentage: 0.4,
          categoryPercentage: 0.3,
        },
      ],
    }

    const commonOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: tooltipItem => {
              const value = tooltipItem.raw
              return (
                tooltipItem.dataset.label +
                ": " +
                formatLargeNumber(value, currency, "en")
              )
            },
          },
        },
      },
    }

    const desktopOptions = {
      ...commonOptions,
      layout: {
        padding: {
          right: 50,
          bottom: 0,
          left: 10,
        },
      },
      scales: {
        x: {
          border: {
            display: false,
          },
          ticks: {
            padding: 20,
            font: {
              size: 16,
            },
            color: "#11152E",
            crossAlign: "far",
          },

          grid: {
            tickLength: 30,
            color: "rgba(0, 0, 0, 0)",
            borderColor: "rgba(0, 0, 0, 0)",
          },
        },
        y: {
          border: {
            display: false,
          },
          beginAtZero: true,
          ticks: {
            callback: value => formatLargeNumber(value, currency, "en"),
            padding: 50, // Adjust this value to control spacing between ticks and labels
            font: {
              size: 16,
            },
            crossAlign: "far",
            color: "#11152E",
            autoSkip: true,
            maxTicksLimit: 4,
          },
          grid: {
            padding: 0,
            color: "rgba(241, 243, 253, 1)",
            borderDash: [3, 4],
            lineWidth: 2,
            drawBorder: false,
            drawTicks: false,
          },
        },
      },
    }

    const mobileOptions = {
      ...commonOptions,
      layout: {
        padding: {
          right: 30,
          bottom: 0,
          left: 10,
        },
      },
      scales: {
        x: {
          border: {
            display: false,
          },
          ticks: {
            padding: 20,
            font: {
              size: 16,
            },
            color: "#11152E",
            crossAlign: "far",
          },

          grid: {
            tickLength: 30,
            color: "rgba(0, 0, 0, 0)",
            borderColor: "rgba(0, 0, 0, 0)",
          },
        },
        y: {
          border: {
            display: false,
          },
          beginAtZero: true,
          ticks: {
            callback: value => formatLargeNumber(value, currency, "en"),
            padding: 30, 
            font: {
              size: 16,
            },
            crossAlign: "far",
            color: "#11152E",
            autoSkip: true,
            maxTicksLimit: 4,
          },
          grid: {
            padding: 0,
            color: "rgba(241, 243, 253, 1)",
            borderDash: [3, 4],
            lineWidth: 2,
            drawBorder: false,
            drawTicks: false,
          },
        },
      },
    }

    return (
      <div className={`tw-pb-2 ${className ?? ""}`}>
        <div className="tw-hidden lg:tw-block tw-relative tw-h-[336px]">
          <Bar data={dataToDisplay} options={desktopOptions} />
        </div>
        <div className="lg:tw-hidden tw-relative tw-h-[336px]">
          <Bar data={dataToDisplay} options={mobileOptions} />
        </div>
      </div>
    )
  }
}

export default BalanceSheetsChart
