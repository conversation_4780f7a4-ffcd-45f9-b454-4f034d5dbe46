import React from "react"
import AssetFinancialBalanceSheets from "./asset-financial-balance-sheets"
import AssetFinancialIncomeStatements from "./asset-financial-income-statements"
import AssetFinancialCashFlows from "./asset-financial-cash-flows"
import DisplayModeButtons, { DisplayModeEnum } from "../display-mode-buttons"

export const TenorsEnum = {
  QUARTERLY: "Quarterly",
  ANNUAL: "Annual",
}

const ReportsEnum = {
  INCOME_STATEMENTS: "Income Statement",
  BALANCE_SHEETS: "Balance Sheet",
  CASH_FLOWS: "Cash Flow",
}

export const financialsResultConfig = {
  positive: {
    textColor: "#23846A",
    backgroundColor: " #DCFAF1",
  },
  neutral: {
    textColor: "#11152E",
    backgroundColor: "#F4F4F4",
  },
  negative: {
    textColor: "#D63C3C",
    backgroundColor: "#FFE6E6",
  },
}

class AssetFinancialSection extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      activeTenor: TenorsEnum.QUARTERLY,
      activeReport: ReportsEnum.INCOME_STATEMENTS,
      activeDisplayMode: DisplayModeEnum.TABLE,
    }
  }

  handleTenorChange = (tenor) => {
    this.setState({ activeTenor: tenor })
  }

  handleReportChange = (report) => {
    this.setState({ activeReport: report })
  }

  handleDisplayModeChange = (displayMode) => {
    this.setState({ activeDisplayMode: displayMode })
  }

  render() {
    const { className, earnings, currency } = this.props
    const { activeTenor, activeReport, activeDisplayMode } = this.state
    const { incomeStatements, cashFlows, balanceSheets } = earnings

    const incomeStatementsToDisplay =
      activeTenor === TenorsEnum.QUARTERLY
        ? incomeStatements.quarterly
        : incomeStatements.annual

    const balanceSheetsToDisplay =
      activeTenor === TenorsEnum.QUARTERLY
        ? balanceSheets.quarterly
        : balanceSheets.annual

    const cashFlowsToDisplay =
      activeTenor === TenorsEnum.QUARTERLY
        ? cashFlows.quarterly
        : cashFlows.annual

    return (
      <section id="financials" className={`noto-sans ${className ?? ""}`}>
        <div className="tw-flex tw-flex-col xl:tw-flex-row tw-gap-4 xl:tw-gap-0 xl:tw-justify-between tw-mb- xl:tw-items-center tw-mb-6">
          <div className="tw-flex tw-justify-between">
            <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl">
              Financial Performance
            </h5>

            <DisplayModeButtons
              className="xl:tw-hidden tw-block"
              activeDisplayMode={activeDisplayMode}
              handleDisplayModeChange={this.handleDisplayModeChange}
            />
          </div>
          <div className="tw-flex tw-gap-4">
            {/* Report type selector */}

            <div className="tw-text-left tw-flex tw-text-neutralPrimary tw-font-semibold !tw-leading-[1.125rem] tw-max-w-[90vw] tw-overflow-x-auto tw-z-10 tw-m-auto xl:tw-m-0 tw-hide-scrollbar-in-small-screens tw-pr-6 sm:tw-px-0 !tw-my-auto">
              <span
                className={`tw-px-4 tw-py-2 cursor-pointer tw-rounded-3xl tw-text-nowrap ${
                  activeReport === ReportsEnum.INCOME_STATEMENTS
                    ? "tw-bg-neutralPrimary tw-text-white"
                    : ""
                }`}
                onClick={() =>
                  this.handleReportChange(ReportsEnum.INCOME_STATEMENTS)
                }
              >
                {ReportsEnum.INCOME_STATEMENTS}
              </span>
              <span
                className={`tw-px-4 tw-py-2 cursor-pointer tw-rounded-3xl tw-text-nowrap ${
                  activeReport === ReportsEnum.BALANCE_SHEETS
                    ? "tw-bg-neutralPrimary tw-text-white"
                    : ""
                }`}
                onClick={() =>
                  this.handleReportChange(ReportsEnum.BALANCE_SHEETS)
                }
              >
                {ReportsEnum.BALANCE_SHEETS}
              </span>
              <span
                className={`tw-px-4 tw-py-2 cursor-pointer tw-rounded-3xl tw-text-nowrap ${
                  activeReport === ReportsEnum.CASH_FLOWS
                    ? "tw-bg-neutralPrimary tw-text-white"
                    : ""
                }`}
                onClick={() => this.handleReportChange(ReportsEnum.CASH_FLOWS)}
              >
                {ReportsEnum.CASH_FLOWS}
              </span>
            </div>
            <div className="tw-hidden xl:tw-block tw-border-l-2 tw-border-primary" />
            {/* End of Report type selector */}
            <DisplayModeButtons
              className="tw-hidden xl:tw-block"
              activeDisplayMode={activeDisplayMode}
              handleDisplayModeChange={this.handleDisplayModeChange}
            />
          </div>
        </div>
        <AssetFinancialIncomeStatements
          currency={currency}
          incomeStatements={incomeStatementsToDisplay}
          activeTenor={activeTenor}
          activeDisplayMode={activeDisplayMode}
          onTenorChangeHandler={this.handleTenorChange}
          className={`${
            activeReport === ReportsEnum.INCOME_STATEMENTS ? "" : "tw-hidden"
          }`}
        />
        <AssetFinancialBalanceSheets
          currency={currency}
          balanceSheets={balanceSheetsToDisplay}
          activeTenor={activeTenor}
          activeDisplayMode={activeDisplayMode}
          onTenorChangeHandler={this.handleTenorChange}
          className={`${
            activeReport === ReportsEnum.BALANCE_SHEETS ? "" : "tw-hidden"
          }`}
        />
        <AssetFinancialCashFlows
          currency={currency}
          cashFlows={cashFlowsToDisplay}
          activeTenor={activeTenor}
          activeDisplayMode={activeDisplayMode}
          onTenorChangeHandler={this.handleTenorChange}
          className={`${
            activeReport === ReportsEnum.CASH_FLOWS ? "" : "tw-hidden"
          }`}
        />
      </section>
    )
  }
}

export default AssetFinancialSection
