import React, { Component } from "react"
import { Doughnut } from "react-chartjs-2"
import { Chart as ChartJS, ArcElement, Tooltip } from "chart.js"
import Decimal from "decimal.js"

ChartJS.register(ArcElement, Tooltip)

class SentimentGaugeChart extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  sentimentRanges = [
    { min: -1.5, max: -0.78, text: "Extreme fear", color: "#D63C3C" },
    { min: -0.77, max: -0.31, text: "Fear", color: "#D63C3C" },
    { min: -0.3, max: 0.3, text: "Neutral", color: "#536AE3" },
    { min: 0.31, max: 0.77, text: "Greed", color: "#23846A" },
    { min: 0.78, max: 1.5, text: "Extreme greed", color: "#23846A" },
  ]

  getSentimentTextAndColor(value) {
    const roundedToTwoDecimals = new Decimal(value).toDecimalPlaces(2)
    const range = this.sentimentRanges.find(
      (range) =>
        roundedToTwoDecimals.greaterThanOrEqualTo(range.min) &&
        roundedToTwoDecimals.lessThanOrEqualTo(range.max)
    )
    return { text: range.text, color: range.color }
  }

  render() {
    const { value, minValue, maxValue } = this.props
    const { text, color } = this.getSentimentTextAndColor(value)

    const data = {
      datasets: [
        {
          data: [value - minValue, maxValue - value],
          backgroundColor: [color, "#F1F3FD"],
          borderWidth: 0,
          cutout: "70%",
        },
      ],
    }

    const options = {
      responsive: true,
      maintainAspectRatio: false,
      rotation: -90,
      circumference: 180,
      plugins: {
        tooltip: { enabled: false },
      },
      elements: {
        arc: {
          borderWidth: 0,
        },
      },
    }

    return (
      <div className="tw-relative noto-sans">
        <div className="tw-h-[227px]">
          <Doughnut data={data} options={options} />
        </div>
        <div className="tw-flex tw-justify-between tw-items-baseline tw-px-4">
          <p className="tw-text-darkGray tw-text-center">{minValue}</p>
          <p className="tw-text-2xl" style={{ color: color }}>
            {text}
          </p>
          <p className="tw-text-darkGray tw-text-base">+{maxValue}</p>
        </div>
        <h3
          style={{
            transform: "translate(-50%, 20%)",
          }}
          className="tw-text-4xl tw-text-[#11152E] tw-self-start tw-absolute tw-left-1/2 tw-top-1/2"
        >
          {value.toFixed(2)}
        </h3>
      </div>
    )
  }
}

export default SentimentGaugeChart
