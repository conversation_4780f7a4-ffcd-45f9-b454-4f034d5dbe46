import React from "react"
import { Line } from "react-chartjs-2"
import SentimentGaugeChart from "./sentiment-gauge-chart"
import { formatDateToDDMONYYYY } from "../../../utils/date"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
)

const options = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false },
    tooltip: {
      enabled: true,
      intersect: false,
      mode: "nearest",
      bodySpacing: 5,
      padding: 10,
      displayColors: false,
      backgroundColor: "#536AE3",
      titleFontColor: "#ffffff",
      cornerRadius: 4,
      callbacks: {
        label(tooltipItem) {
          return `Score: ${tooltipItem.raw}`
        },
      },
    },
  },
  scales: {
    x: {
      display: false,
      grid: {
        tickMarkLength: 30,
        color: "rgba(0, 0, 0, 0)",
        zeroLineColor: "rgba(0, 0, 0, 0)",
      },
      ticks: {
        display: true,
        beginAtZero: false,
        color: "#000",
        fontSize: 16,
        padding: 0,
        maxTicksLimit: 2.1,
        autoSkip: true,
        maxRotation: 0,
        minRotation: 0,
      },
    },
    y: {
      display: true,
      position: "right",
      min: -1.55,
      max: 1.55,
      border: {
        display: false,
      },
      grid: {
        color: "rgba(236, 240, 241, 1)",
        offset: false,
        drawTicks: false,
        borderDash: [3, 4],
        lineWidth: 1,
        zeroLineWidth: 0,
        z: -1,
      },
      ticks: {
        display: true,
        beginAtZero: false,
        color: "#000",
        fontSize: 16,
        padding: 20,
        maxTicksLimit: 3,
        stepSize: 1.5,
        callback: function (value) {
          if (value === -1.55) return -1.5
          else if (value === 0) return value
          else if (value === 1.55) return 1.5
          return ""
        },
      },
    },
  },
  elements: {
    line: {
      tension: 0.1,
      z: 1,
    },
    point: {
      radius: 0,
      z: 1,
    },
  },
  layout: {
    padding: {
      left: 0,
      right: 0,
      top: 20,
      bottom: 10,
    },
  },
}

const SentimentChart = ({ chartData }) => (
  <div className="tw-flex tw-flex-col tw-justify-between tw-h-full">
    <div className="tw-h-[227px]">
      <Line data={chartData} options={options} />
    </div>
    <p className="tw-text-neutralPrimary tw-text-left tw-font-semibold tw-text-base tw-pl-1">
      Last 30 days
    </p>
  </div>
)

class SentimentAnalysis extends React.Component {
  render() {
    const { className, sentimentScore, chartData } = this.props

    if (!sentimentScore) return <></>

    const formattedChartData = {
      labels: chartData.map((item) =>
        formatDateToDDMONYYYY(new Date(item.date))
      ),
      datasets: [
        {
          label: "Sentiment Score",
          data: chartData.map((item) => item.score),
          borderColor: "#5d78ff",
          fill: false,
          borderWidth: 2,
        },
      ],
    }

    return (
      <section id="sentiment" className={`${className ?? ""}`}>
        <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl tw-mb-12">
          Sentiment analysis
        </h5>
        <div className="tw-flex tw-flex-col lg:tw-flex-row tw-gap-7">
          <div className="lg:tw-basis-1/2">
            <SentimentGaugeChart
              value={sentimentScore}
              minValue={-1.5}
              maxValue={1.5}
            />
          </div>
          <div className="lg:tw-basis-1/2">
            <SentimentChart chartData={formattedChartData} />
          </div>
        </div>
      </section>
    )
  }
}

export default SentimentAnalysis
