import React from "react"

class EtfTopHoldingsBreakdownSection extends React.Component {
  render() {
    const { topHoldingsBreakDown, className } = this.props

    return (
      <section id="top-10-holdings" className={`noto-sans ${className ?? ""}`}>
        <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl tw-mb-8">
          Top 10 Holdings
        </h5>
        <div className="tw-grid tw-gap-6 tw-grid-cols-1 lg:tw-grid-cols-3">
          {topHoldingsBreakDown.map((el, index) => (
            <div key={`holding-${index}`} className="tw-flex tw-gap-3">
              <div className="tw-flex tw-justify-center tw-items-center tw-border-[#f8f9fa] tw-border-2 tw-border-solid tw-rounded-[20%] tw-h-14 tw-w-14">
                <img
                  src={el.logoUrl}
                  className="tw-w-full tw-max-h-full tw-rounded-[20%]"
                  alt="provider logo"
                />
              </div>
              <div className="tw-flex tw-flex-col tw-p-1 gap-1">
                <h6 className="tw-text-neutralPrimary tw-font-semibold tw-text-lg">
                  {el.name}
                </h6>
                <p className="tw-text-primary tw-text-lg">{el.weight}</p>
              </div>
            </div>
          ))}
        </div>
      </section>
    )
  }
}

export default EtfTopHoldingsBreakdownSection
