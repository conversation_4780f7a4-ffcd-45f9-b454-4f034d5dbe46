import Decimal from "decimal.js"
import React from "react"
import { formatPercentageLocale } from "../../utils/formatterUtil"

class EtfRegionalBreakdownSection extends React.Component {
  render() {
    const { regionalBreakdown, locale, className } = this.props
    if (!regionalBreakdown) return <></>

    return (
      <section
        id="regional-breakdown"
        className={`noto-sans ${className ?? ""}`}
      >
        <h5 className="tw-font-semibold tw-text-neutralPrimary tw-text-2xl tw-mb-6">
          Regional breakdown
        </h5>
        <div className="tw-grid tw-gap-3 tw-grid-cols-1 lg:tw-grid-cols-2 lg:tw-gap-x-40">
          {regionalBreakdown.map((el, index) => (
            <div key={`region-${index}`} className="tw-flex tw-py-2">
              <p className="tw-m-0 tw-text-neutralPrimary tw-w-full tw-font-semibold tw-text-lg">
                {el.name}
              </p>
              <div className="tw-w-full">
                <div className="tw-w-full tw-h-[6px] tw-bg-[#F4F4F4] tw-rounded-full tw-overflow-hidden tw-relative">
                  <div
                    className="tw-h-full tw-bg-primary tw-rounded-r-full tw-absolute tw-right-0"
                    style={{ width: `${el.percentage}%` }}
                  />
                </div>
                <p className="tw-text-primary tw-text-lg tw-text-right tw-m-0">
                  {formatPercentageLocale(
                    Decimal.div(el.percentage, 100).toNumber(),
                    locale
                  )}
                </p>
              </div>
            </div>
          ))}
        </div>
      </section>
    )
  }
}

export default EtfRegionalBreakdownSection
