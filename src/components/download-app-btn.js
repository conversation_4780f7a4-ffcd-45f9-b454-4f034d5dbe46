import React from "react"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { DictionaryEnum } from "../translations/dictionary-entries"
import { isRegionPrefixGreece } from "../utils/languageUtils"
import GreeceWaitlistButton from "./greece-waitlist-button"
import LocaleEnum from "../configs/locale-config"

const FREE_ETF_WLTHD_ID = "yfset5ax" // this corresponds to a Wealthyhood participant
const ONELINK = "https://wealthyhood.onelink.me/TwZO/b236dq9r"
const ONELINK_REWARD = "https://wealthyhood.onelink.me/TwZO/wcu96dyg"
const BTN_CONFIG = {
  dark: "btn-primary bg-dark-blue",
  light: "btn-light",
}

class DownloadAppBtn extends React.Component {
  #displayQRCodeModal = () => {
    window.eventEmitter.emit("DISPLAY_QR_CODE_MODAL")
  }

  #fireGtmEvent = () => {
    const anonymousId = this.#getAnonymousId()
    this.#setGoogleUserId(anonymousId)

    window.dataLayer = window.dataLayer || []
    window.dataLayer.push({
      event: "downloadAppClick",
    })
    window.dataLayer.push({
      event: "downloadAppClickUserID",
      userId: anonymousId,
    })
  }

  #getPageUserLanded = () => {
    if (
      window &&
      window.cookieManager &&
      typeof window.cookieManager.get === "function"
    ) {
      return window.cookieManager.get("pageUserLanded")
    }
  }

  #getAnonymousId = () => {
    if (
      window &&
      window.cookieManager &&
      typeof window.cookieManager.get === "function"
    ) {
      return window.cookieManager.get("anonymousId")
    }
  }

  #getAttributionParams = () => {
    // In case the user is in the /get-reward and has been linked there from Finance Ads, we want to attribute
    // that user to Finance Ads and therefore do not set referral parameters.
    if (
      window &&
      window.location.href.includes("/get-reward") &&
      this.#getInfluencerId()
    ) {
      // return only the sid
      return { sid: this.#getInfluencerId() }
    } else {
      const urlParams = new window.URLSearchParams(window.location.search)
      const grsfId = urlParams.get("grsf")

      if (grsfId) {
        return { grsf: grsfId }
      } else {
        const wlthdId = urlParams.get("wlthd")
        return window.location.href.includes("/get-reward")
          ? { wlthd: FREE_ETF_WLTHD_ID }
          : { wlthd: wlthdId }
      }
    }
  }

  /**
   * _ga has the format GA1.1.xxxxxxxxxx.xxxxxxxxxx
   * @returns the client format in the format xxxxxxxxxx.xxxxxxxxxx
   */
  #getGaClientId = () => {
    if (
      window &&
      window.cookieManager &&
      typeof window.cookieManager.get === "function"
    ) {
      const gaCookie = window.cookieManager.get("_ga")

      if (gaCookie) {
        const [, , gaClientId, gaTimestamp] = gaCookie.split(".")
        return `${gaClientId}.${gaTimestamp}`
      } else {
        return ""
      }
    }
  }

  #getInfluencerId = () => {
    if (window) {
      const params = new window.URLSearchParams(window.location.search)

      let cachedInfluencerId = ""
      if (
        window.cookieManager &&
        typeof window.cookieManager.get === "function"
      ) {
        cachedInfluencerId = window.cookieManager.get("sID")
      }

      const influencerId = cachedInfluencerId || params.get("s_id")
      return influencerId
    }
  }

  #getOnelink = () => {
    if (window && window.AF_SMART_SCRIPT) {
      const { wlthd, grsf, sid } = this.#getAttributionParams()
      const anonymousId = this.#getAnonymousId()
      const pageUserLanded = this.#getPageUserLanded()
      const gaClientId = this.#getGaClientId()

      //Initializing Smart Script arguments
      let oneLinkURL = ONELINK
      if (window.location.href.includes("/reward")) {
        oneLinkURL = ONELINK_REWARD
      }

      const mediaSource = {
        keys: ["cutm_source", "utm_source"],
        defaultValue: "main_onelink_source",
      }
      const campaign = { keys: ["utm_campaign"] }
      const wlthdParam = {
        paramKey: "wlthd",
        keys: ["wlthd"],
        defaultValue: wlthd || "",
      }
      const grsfParam = {
        paramKey: "grsf",
        keys: ["grsf"],
        defaultValue: grsf || "",
      }
      const sidParam = {
        paramKey: "sid",
        keys: ["sid"],
        defaultValue: sid || "",
      }
      const anonymousIdParam = {
        paramKey: "anonymousId",
        keys: ["anonymousId"],
        defaultValue: anonymousId || "",
      }
      const pageUserLandedParam = {
        paramKey: "pageUserLanded",
        keys: ["pageUserLanded"],
        defaultValue: pageUserLanded || "",
      }
      const gaClientIdParam = {
        paramKey: "gaClientId",
        keys: ["gaClientId"],
        defaultValue: gaClientId || "",
      }

      const custom_ss_ui = { paramKey: "af_ss_ui", defaultValue: "true" }
      const custom_ss_gtm_ui = {
        paramKey: "af_ss_gtm_ui",
        defaultValue: "true",
      }

      //Function is embedded on the window object in a global parameter called window.AF_SMART_SCRIPT.
      //Onelink URL is generated.
      window.AF_SMART_SCRIPT_RESULT = window.AF_SMART_SCRIPT.generateOneLinkURL(
        {
          oneLinkURL,
          afParameters: {
            mediaSource,
            campaign,
            googleClickIdKey: "af_sub4",
            afCustom: [
              wlthdParam,
              grsfParam,
              sidParam,
              anonymousIdParam,
              pageUserLandedParam,
              gaClientIdParam,
              custom_ss_ui,
              custom_ss_gtm_ui,
            ],
          },
        }
      )
      return window.AF_SMART_SCRIPT_RESULT
        ? window.AF_SMART_SCRIPT_RESULT.clickURL
        : oneLinkURL
    } else {
      let oneLinkURL = ONELINK
      if (window.location.href.includes("/reward")) {
        oneLinkURL = ONELINK_REWARD
      }
      return oneLinkURL
    }
  }

  #onButtonClick = () => {
    this.#fireGtmEvent()
    window.location.href = this.#getOnelink()
  }

  #setGoogleUserId = (userId) => {
    console.log("btn.setGoogleUserId - " + userId)
    if (window && window.gtag) {
      console.log("btn.setGoogleUserId - setting user id")
      window.gtag("set", "user_id", userId)
    } else {
      if (!window) {
        console.log("btn.setGoogleUserId - window does not exist")
      } else if (!window.gtag) {
        console.log("btn.setGoogleUserId - gtag does not exist")
      }
    }
  }

  #renderGreeceWaitlistButton = () => {
    return (
      <GreeceWaitlistButton
        children={this.props.children}
        className={this.props.className}
        displayOnMobile={this.props.displayOnMobile}
        color={this.props.color}
      />
    )
  }

  #renderLoader = () => {
    const { children, className, translate, displayOnMobile } = this.props
    const childrenIsImg = children?.type === "img"
    const childrenIsSpan = children?.type === "span"
    let buttonContent
    if (childrenIsSpan) {
      buttonContent = (
        <span className={children.props.className}>
          <div className="tw-min-w-[150px]">
            <div class="spinner-border"></div>
          </div>
        </span>
      )
    } else if (childrenIsImg) {
      // If it's an img, render the child as is
      buttonContent = children
    } else {
      buttonContent = (
        <div className="tw-min-w-[150px]">
          <div class="spinner-border"></div>
        </div>
      )
    }

    return (
      <>
        <button className={`${this.getButtonCss()} ${className ?? ""}`}>
          {buttonContent}
        </button>
        {displayOnMobile && (
          <>
            <button className="noto-sans cta cta-button btn btn-primary button-tall text-medium bg-dark-blue d-block d-lg-none">
              {buttonContent}
            </button>
            <p className="tw-text-sm tw-text-neutralPrimary noto-sans tw-text-center tw-mt-[6px]">
              {translate(DictionaryEnum.CAPITAL_AT_RISK)}
            </p>
          </>
        )}
      </>
    )
  }

  getButtonCss() {
    const { children, color } = this.props
    const btnColor = color || "dark"

    if (!children) {
      return `cta cta-button btn  button-tall text-medium d-none d-lg-block ${BTN_CONFIG[btnColor]}`
    } else {
      return "btn btn-clean"
    }
  }

  render() {
    const {
      displayOnMobile,
      children,
      className,
      translate,
      region,
      locale,
      urlPrefix,
    } = this.props

    if (!region) {
      return this.#renderLoader()
    }

    if (isRegionPrefixGreece(urlPrefix, region) || locale === LocaleEnum.EU) {
      return this.#renderGreeceWaitlistButton()
    }

    return (
      <>
        {/* CTA for desktop - displays QR code */}
        <button
          className={`${this.getButtonCss()} ${className ?? ""}`}
          onClick={() => this.#displayQRCodeModal()}
        >
          {children ?? translate(DictionaryEnum.COMMON_START_INVESTING)}
        </button>
        {/* CTA for mobile - redirects to app store */}
        {displayOnMobile ? (
          <>
            <button
              onClick={() => this.#onButtonClick()}
              className="noto-sans cta cta-button btn btn-primary button-tall text-medium bg-dark-blue d-block d-lg-none"
            >
              {children ?? translate(DictionaryEnum.COMMON_START_INVESTING)}
            </button>
            <p className="tw-text-sm tw-text-neutralPrimary noto-sans tw-text-center tw-mt-[6px]">
              {translate(DictionaryEnum.CAPITAL_AT_RISK)}
            </p>
          </>
        ) : (
          <></>
        )}
        {/* End CTA for mobile */}
      </>
    )
  }
}

export default withLanguageRegion(DownloadAppBtn)
