import React, { Component } from "react"
import { Modal } from "react-bootstrap"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { DictionaryEnum } from "../translations/dictionary-entries"
import LocaleEnum from "../configs/locale-config"
import { getRegionFromUrlPrefix } from "../utils/languageUtils"

const MODAL_TEXTS_CONFIG = {
  [LocaleEnum.UK]: {
    title: DictionaryEnum.GREECE_WAITLIST_MODAL_TITLE,
  },
  [LocaleEnum.EU]: {
    title: DictionaryEnum.GREECE_WAITLIST_MODAL_TITLE_EU,
  },
}

class GreeceWaitlistModal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      email: "",
    }
  }

  handleInputChange = (event) => {
    this.setState({ email: event.target.value })
  }

  _emailInputIsValid = () => {
    const { email } = this.state
    return email && email.trim() !== ""
  }

  _handleSubmit = (event) => {
    event.preventDefault()

    if (!this._emailInputIsValid()) {
      return
    }

    const { region, urlPrefix } = this.props
    const { email } = this.state
    const formRegion = getRegionFromUrlPrefix(urlPrefix, region) || ""

    // Encode form data for Netlify
    const formData = new FormData()
    formData.append("form-name", "greece-waitlist-email-form")
    formData.append("email", email)
    formData.append("region", formRegion)

    fetch("/", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams(formData).toString(),
    })
      .then(() => {
        // Success - you might want to show a success message
        console.log("Form submitted successfully")
        this.setState({ email: "" })
      })
      .catch((error) => {
        console.error("Form submission error:", error)
      })
  }

  render() {
    const { handleClose, show, translate, locale, region, urlPrefix } =
      this.props
    const { email } = this.state
    const { title } = MODAL_TEXTS_CONFIG[locale]

    return (
      <Modal
        show={show}
        onHide={handleClose}
        dialogClassName="greece-waitlist-dialog-position-class p-0"
        size="lg"
        centered
      >
        <Modal.Body className="p-0 noto-sans">
          <div id="greece-waitlist-modal" className="d-flex flex-column">
            <div className="greece-waitlist-modal-mock tw-hidden justify-content-center md:tw-flex">
              <img
                className="tw-object-contain"
                src="/img/greece-waitlist-modal-mock.png"
                alt="wealthyhood app mock"
              />
            </div>
            <div className="md:p-5 lg:tw-p-[32px] md:tw-p-[32px] tw-p-6 tw-pt-9">
              <div className="tw-mb-8 text-center">
                <h4 className="md:text-center noto-sans tw-text-[24px]">
                  {translate(title)}
                </h4>
                <p
                  className="tw-mt-4 tw-text-lg tw-text-neutralPrimary tw-font-normal tw-leading-7"
                  dangerouslySetInnerHTML={{
                    __html: translate(
                      DictionaryEnum.GREECE_WAITLIST_MODAL_DESCRIPTION_HTML
                    ),
                  }}
                />
              </div>
              <div className="tw-flex tw-flex-col tw-items-start">
                <form
                  className="tw-flex tw-flex-col tw-gap-4 tw-w-full tw-rounded-[3.625rem]"
                  name="greece-waitlist-email-form"
                  data-netlify="true"
                  autoComplete="off"
                  data-1p-ignore="true"
                  id="greece-waitlist-email-form"
                  onSubmit={this._handleSubmit}
                >
                  <input
                    type="hidden"
                    name="form-name"
                    value="greece-waitlist-email-form"
                    autoComplete="off"
                    data-1p-ignore="true"
                  />
                  <div className="greece-waitlist-input-container">
                    <input
                      type="hidden"
                      name="region"
                      value={getRegionFromUrlPrefix(urlPrefix, region) || ""}
                    />
                    <input
                      name="email"
                      className="tw-py-3 tw-px-8 tw-text-neutralPrimary tw-border-0 flex-grow-1 noto-sans"
                      type="email"
                      placeholder={translate(DictionaryEnum.WRITE_YOUR_EMAIL)}
                      value={email}
                      onChange={this.handleInputChange}
                      autoComplete="off"
                      data-1p-ignore="true"
                    />
                    <button
                      type="submit"
                      disabled={!this._emailInputIsValid()}
                      className={`hover:tw-bg-opacity-90 tw-px-8 tw-py-4 tw-bg-neutralPrimary tw-rounded-[3.625rem] tw-text-lg tw-font-semibold tw-cursor-pointer tw-text-white tw-flex tw-items-center tw-justify-center ${
                        !this._emailInputIsValid()
                          ? "tw-opacity-50 tw-cursor-not-allowed"
                          : ""
                      }`}
                    >
                      {translate(DictionaryEnum.GREECE_WAITLIST_MODAL_BUTTON)}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    )
  }
}

export default withLanguageRegion(GreeceWaitlistModal)
