import React from "react"
import { <PERSON> } from "gatsby"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { DictionaryEnum } from "../translations/dictionary-entries"
import { getLanguageFromLanguageRegion } from "../utils/languageUtils"

const MobileAnnouncementBanner = ({
  className = "",
  languageRegion,
  translate,
}) => {
  const language = getLanguageFromLanguageRegion(languageRegion)

  return (
    <div
      className={`${className}`}
      style={{
        minHeight: "72px",
        width: "100%",
        background:
          "linear-gradient(90deg, #536AE3 0%, #3A49B8 50%, #536AE3 100%)",
        position: "fixed",
        top: 0,
        left: 0,
        zIndex: 1031,
      }}
    >
      <div
        style={{
          width: "100%",
          marginLeft: "auto",
          marginRight: "auto",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          paddingLeft: "0.5rem",
          paddingRight: "0.5rem",
          paddingTop: "0.75rem",
          paddingBottom: "0.75rem",
        }}
      >
        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "center",
            gap: "0.5rem",
          }}
        >
          <div
            className="noto-sans"
            style={{
              color: "#ffffff",
              fontWeight: 400,
              fontSize: "0.875rem",
              textAlign: "center",
              wordBreak: "normal",
              overflowWrap: "normal",
            }}
          >
            {translate(DictionaryEnum.ANNOUNCEMENT_BANNER_TITLE)}
          </div>
          <Link
            to={`/${language}/press-releases/wealthyhood-raises-new-seed-round/`}
            className="noto-sans"
            style={{
              color: "#11152E",
              backgroundColor: "#ffffff",
              borderRadius: "9999px",
              paddingLeft: "0.75rem",
              paddingRight: "0.75rem",
              paddingTop: "0.25rem",
              paddingBottom: "0.25rem",
              fontWeight: 600,
              fontSize: "0.75rem",
              whiteSpace: "nowrap",
            }}
          >
            {translate(DictionaryEnum.ANNOUNCEMENT_BANNER_CTA)}
          </Link>
        </div>
      </div>
    </div>
  )
}

export default withLanguageRegion(MobileAnnouncementBanner)
