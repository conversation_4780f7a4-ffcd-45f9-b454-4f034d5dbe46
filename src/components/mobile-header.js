import { <PERSON> } from "gatsby"
import React from "react"
import { Link as ScrollLink } from "react-scroll"
import { HEADER_LINKS } from "../configs/links-config"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { LanguageEnum } from "../configs/language-region-config"
import { DictionaryEnum } from "../translations/dictionary-entries"
import {
  getLanguageDisplayFlag,
  getLanguageDisplayName,
  getLanguageFromLanguageRegion,
  navigateToLanguage,
  shouldDisplayLanguagePicker,
} from "../utils/languageUtils"

/**
 * Language Button component
 * @param {Object} props - Component props
 * @param {string} props.languageRegion - Current language region
 * @param {Function} props.onOpenLanguageSelection - Function to open language selection
 * @returns {JSX.Element} Language button component
 */
const LanguageButton = ({ languageRegion, onOpenLanguageSelection }) => {
  const currentLanguage = getLanguageFromLanguageRegion(languageRegion)

  return (
    <li className="navbar-nav-item pl-3">
      <div className="nav-link noto-sans !tw-text-base !tw-font-medium tw-text-[#10132799]">
        <div
          onClick={onOpenLanguageSelection}
          className="tw-flex tw-items-center tw-gap-2 tw-cursor-pointer"
        >
          <div className="tw-flex tw-items-center tw-justify-center tw-h-6 tw-w-6 tw-rounded-full tw-border tw-border-[rgba(16,19,39,0.05)]">
            <img
              src={`/img/flags/${getLanguageDisplayFlag(currentLanguage)}`}
              alt={`${getLanguageDisplayName(currentLanguage)} flag`}
              className="tw-h-full tw-w-full tw-rounded-full tw-object-cover"
            />
          </div>
          <span>{getLanguageDisplayName(currentLanguage)}</span>
        </div>
      </div>
    </li>
  )
}

/**
 * Language Selection component
 * @param {Object} props - Component props
 * @param {Function} props.translate - Translation function
 * @param {string} props.languageRegion - Current language region
 * @param {Function} props.onCloseLanguageSelection - Function to close language selection
 * @returns {JSX.Element} Language selection component
 */
const LanguageSelection = ({
  translate,
  languageRegion,
  onCloseLanguageSelection,
}) => {
  const currentLanguage = getLanguageFromLanguageRegion(languageRegion)

  const getLanguageSelectionClassName = (languageOption) => {
    return currentLanguage === languageOption
      ? "tw-bg-[#F1F3FD] tw-rounded-3xl"
      : "hover:tw-bg-gray-50"
  }

  const handleLanguageChange = (language) => {
    navigateToLanguage(language, onCloseLanguageSelection)
  }

  return (
    <div className="tw-w-full">
      <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
        <h3 className="tw-text-lg tw-font-medium">
          {translate(DictionaryEnum.COMMON_SELECT_LANGUAGE)}
        </h3>
      </div>

      <div className="tw-space-y-2">
        <button
          onClick={() => handleLanguageChange(LanguageEnum.English)}
          className={`tw-flex tw-items-center tw-w-full tw-py-3 tw-px-3 tw-text-left ${getLanguageSelectionClassName(LanguageEnum.English)}`}
        >
          <div className="tw-flex tw-items-center tw-gap-3">
            <div className="tw-flex tw-items-center tw-justify-center tw-h-8 tw-w-8 tw-rounded-full tw-border tw-border-[rgba(16,19,39,0.05)]">
              <img
                src={`/img/flags/${getLanguageDisplayFlag(LanguageEnum.English)}`}
                alt={`${getLanguageDisplayName(LanguageEnum.English)} flag`}
                className="tw-h-full tw-w-full tw-rounded-full tw-object-cover"
              />
            </div>
            <span className="tw-font-[Noto Sans] tw-text-base tw-text-[#15141F] tw-leading-[1.5em]">
              {getLanguageDisplayName(LanguageEnum.English)}
            </span>
          </div>
        </button>

        <button
          onClick={() => handleLanguageChange(LanguageEnum.Greek)}
          className={`tw-flex tw-items-center tw-w-full tw-py-3 tw-px-3 tw-text-left ${getLanguageSelectionClassName(LanguageEnum.Greek)}`}
        >
          <div className="tw-flex tw-items-center tw-gap-3">
            <div className="tw-flex tw-items-center tw-justify-center tw-h-8 tw-w-8 tw-rounded-full tw-border tw-border-[rgba(16,19,39,0.05)]">
              <img
                src={`/img/flags/${getLanguageDisplayFlag(LanguageEnum.Greek)}`}
                alt={`${getLanguageDisplayName(LanguageEnum.Greek)} flag`}
                className="tw-h-full tw-w-full tw-rounded-full tw-object-cover"
              />
            </div>
            <span className="tw-font-[Noto Sans] tw-text-base tw-text-[#15141F] tw-leading-[1.5em]">
              {getLanguageDisplayName(LanguageEnum.Greek)}
            </span>
          </div>
        </button>
      </div>
    </div>
  )
}

class MobileHeader extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isLanguageSelectionOpen: false,
      wasLanguageSelectionOpen: false,
    }
  }

  handleLanguageSelectionOpen = () => {
    this.setState({ isLanguageSelectionOpen: true })
  }

  handleLanguageSelectionClose = () => {
    this.setState({ isLanguageSelectionOpen: false })
  }

  render() {
    const {
      activePage,
      backgroundColor,
      translate,
      urlPrefix,
      languageRegion,
      region,
    } = this.props
    const { isLanguageSelectionOpen } = this.state

    return (
      <header
        id="header"
        className={`header header-bg-transparent noto-sans pt-4"`}
        style={{
          backgroundColor: backgroundColor,
          marginTop: "60px", // TODO: remove this margin once announcement banner is removed
        }}
      >
        <div
          id="navBar"
          className="collapse navbar-collapse position-absolute"
          style={{ top: 30, left: 0, width: "100%" }}
        >
          <ul
            className="container navbar-nav mobile-navbar"
            style={{
              width: "90%",
              height: "auto !important", // This is crucial - allows height to adjust to content
            }}
          >
            {isLanguageSelectionOpen ? (
              // Show language selection UI when language picker is clicked
              <li className="navbar-nav-item">
                <LanguageSelection
                  translate={translate}
                  languageRegion={languageRegion}
                  onCloseLanguageSelection={this.handleLanguageSelectionClose}
                />
              </li>
            ) : (
              // Show regular navigation when language selection is closed
              <>
                {HEADER_LINKS.map((linkItem) => {
                  if (linkItem.type === "scroll" && activePage === "index") {
                    return (
                      <li key={linkItem.slug} className="navbar-nav-item">
                        <ScrollLink
                          className={`nav-link noto-sans !tw-text-base !tw-font-medium tw-text-[#10132799] ${
                            activePage === linkItem.slug ||
                            linkItem.slugsToBeActive?.includes(activePage)
                              ? "text-light-blue font-weight-bold selected"
                              : ""
                          }`}
                          to={linkItem.domID}
                          spy={true}
                          smooth={true}
                          offset={70}
                          duration={500}
                          style={{ cursor: "pointer" }}
                        >
                          {translate(linkItem.label)}
                        </ScrollLink>
                      </li>
                    )
                  } else
                    return (
                      <li key={linkItem.slug} className="navbar-nav-item">
                        <Link
                          className={`nav-link noto-sans !tw-text-base !tw-font-medium tw-text-[#10132799] py-0 ${
                            activePage === linkItem.slug ||
                            linkItem.slugsToBeActive?.includes(activePage)
                              ? "text-light-blue font-weight-bold selected"
                              : ""
                          }`}
                          to={`/${urlPrefix}/${linkItem.slug}`}
                        >
                          {translate(linkItem.label)}
                        </Link>
                      </li>
                    )
                })}

                <li className="navbar-nav-item d-none">
                  <button
                    id="get-the-app-header"
                    type="submit"
                    className={
                      "cta btn btn-primary cta-button button-short text-medium bg-dark-blue"
                    }
                  >
                    <span className="d-flex justify-content-center">
                      <span>
                        <svg
                          width="16"
                          height="19"
                          viewBox="0 0 16 19"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className="align-content-center mr-2 mb-0"
                        >
                          <path
                            d="M13.2542 10.0628C13.2808 12.7868 15.7692 13.6933 15.7968 13.7048C15.7758 13.7687 15.3992 14.9966 14.4858 16.2649C13.6961 17.3615 12.8767 18.4539 11.5857 18.4766C10.3172 18.4988 9.90929 17.7619 8.45902 17.7619C7.0092 17.7619 6.55601 18.454 5.35523 18.4988C4.10912 18.5436 3.16018 17.3131 2.36405 16.2206C0.737171 13.9858 -0.5061 9.9057 1.1633 7.15155C1.99262 5.78383 3.47468 4.91773 5.08332 4.89552C6.30697 4.87334 7.46192 5.6777 8.20998 5.6777C8.95758 5.6777 10.3611 4.71039 11.8366 4.85245C12.4543 4.87688 14.1882 5.08952 15.3015 6.63794C15.2118 6.69078 13.2327 7.78549 13.2542 10.0628ZM10.8702 3.37397C11.5317 2.6131 11.977 1.5539 11.8555 0.5C10.9019 0.536416 9.74881 1.10377 9.06481 1.86422C8.45182 2.53763 7.91497 3.61548 8.05982 4.64852C9.12272 4.72665 10.2086 4.13532 10.8702 3.37397Z"
                            fill="white"
                          />
                        </svg>
                      </span>
                      <span>
                        <svg
                          width="16"
                          height="19"
                          viewBox="0 0 16 19"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className="align-content-center mr-3 mb-0"
                        >
                          <path
                            d="M3.17393 13.8902C3.17393 14.3374 3.53898 14.7025 3.98812 14.7025H4.9189V17.0036C4.9189 17.5865 5.38716 18.0624 5.96244 18.0624C6.53964 18.0624 7.00598 17.5884 7.00598 17.0036V14.7005H8.63054V17.0017C8.63054 17.5846 9.0988 18.0605 9.67408 18.0605C10.2513 18.0605 10.7176 17.5865 10.7176 17.0017V14.7005H11.6484C12.0975 14.7005 12.4626 14.3355 12.4626 13.8883V6.33501H3.17393V13.8902V13.8902ZM9.99899 2.42078L10.8571 1.17847C10.9068 1.10584 10.8954 1.00837 10.8285 0.960587C10.7616 0.914717 10.666 0.937652 10.6163 1.01028L9.72569 2.30228C9.13893 2.07102 8.48529 1.94297 7.79915 1.94297C7.11301 1.94297 6.45936 2.07102 5.87261 2.30228L4.98197 1.01219C4.93228 0.939563 4.83672 0.914717 4.76982 0.962498C4.70293 1.00837 4.69146 1.10393 4.74115 1.18038L5.5993 2.42269C4.23467 3.05722 3.27905 4.2594 3.15673 5.66417H12.4454C12.3192 4.25749 11.3617 3.05531 9.99899 2.42078V2.42078ZM5.83821 4.37981C5.73653 4.37981 5.63712 4.34965 5.55258 4.29316C5.46803 4.23667 5.40213 4.15637 5.36322 4.06243C5.32431 3.96849 5.31413 3.86511 5.33396 3.76538C5.3538 3.66565 5.40277 3.57404 5.47467 3.50214C5.54657 3.43024 5.63818 3.38127 5.73791 3.36144C5.83764 3.3416 5.94101 3.35178 6.03496 3.39069C6.1289 3.42961 6.2092 3.4955 6.26569 3.58005C6.32218 3.6646 6.35234 3.764 6.35234 3.86568C6.35198 4.00193 6.2977 4.13249 6.20136 4.22883C6.10502 4.32517 5.97446 4.37945 5.83821 4.37981V4.37981ZM9.82125 4.37981C9.71956 4.37981 9.62016 4.34965 9.53562 4.29316C9.45107 4.23667 9.38517 4.15637 9.34626 4.06243C9.30735 3.96849 9.29716 3.86511 9.317 3.76538C9.33684 3.66565 9.38581 3.57404 9.45771 3.50214C9.52961 3.43024 9.62122 3.38127 9.72095 3.36144C9.82068 3.3416 9.92405 3.35178 10.018 3.39069C10.1119 3.42961 10.1922 3.4955 10.2487 3.58005C10.3052 3.6646 10.3354 3.764 10.3354 3.86568C10.335 4.00193 10.2807 4.13249 10.1844 4.22883C10.0881 4.32517 9.95749 4.37945 9.82125 4.37981ZM14.091 6.30443C13.5138 6.30443 13.0474 6.77842 13.0474 7.36327V11.4992C13.0474 12.0821 13.5157 12.558 14.091 12.558C14.6682 12.558 15.1345 12.084 15.1345 11.4992V7.36135C15.1364 6.77651 14.6701 6.30443 14.091 6.30443ZM1.5035 6.30443C0.926305 6.30443 0.459961 6.77842 0.459961 7.36327V11.4992C0.459961 12.0821 0.928216 12.558 1.5035 12.558C2.0807 12.558 2.54704 12.084 2.54704 11.4992V7.36135C2.54704 6.77651 2.07879 6.30443 1.5035 6.30443Z"
                            fill="white"
                          />
                        </svg>
                      </span>
                      <p>{translate(DictionaryEnum.COMMON_GET_THE_APP)}</p>
                    </span>
                  </button>
                </li>

                {shouldDisplayLanguagePicker(urlPrefix, region) && (
                  <LanguageButton
                    languageRegion={languageRegion}
                    onOpenLanguageSelection={this.handleLanguageSelectionOpen}
                  />
                )}

                <li className="navbar-nav-item pr-0">
                  <a
                    href={"https://app.wealthyhood.com/auth"}
                    id="login-header"
                    className={
                      "cta btn btn-primary cta-button button-short text-medium bg-light-blue no-bold-hover mt-2"
                    }
                  >
                    <span className="d-flex justify-content-center">
                      <p className="mt-0 mb-0">
                        {translate(DictionaryEnum.COMMON_LOGIN)}
                      </p>
                    </span>
                  </a>
                </li>
              </>
            )}
          </ul>
        </div>

        <div className="header-section header-bg-transparent">
          <div
            id="logoAndNav"
            className="container bg-white py-3 px-4 w-90"
            style={{
              borderRadius: "16px",
            }}
          >
            {/* <!-- Nav --> */}
            <nav className="navbar navbar-expand-lg">
              {/* <!-- Logo --> */}
              <Link
                className="navbar-brand"
                to={`/${urlPrefix}/`}
                aria-label="Wealthyhood"
                style={{ width: "13rem" }}
              >
                <img
                  src={"/svg/logo-full-dark.svg"}
                  style={{ height: "35px", width: "208px" }}
                  alt="Wealthyhood Logo"
                />
              </Link>
              {/* <!-- End Logo --> */}

              {/* <!-- Responsive Toggle Button --> */}
              <button
                type="button"
                className="navbar-toggler btn btn-icon btn-sm rounded-circle"
                aria-label={translate(DictionaryEnum.COMMON_TOGGLE_NAVIGATION)}
                aria-expanded="false"
                aria-controls="navBar"
                data-toggle="collapse"
                data-target="#navBar"
                onClick={() => this.handleLanguageSelectionClose()}
              >
                <span className="navbar-toggler-default">
                  <img
                    src={"/svg/burger.svg"}
                    alt={translate(DictionaryEnum.COMMON_OPEN_MENU)}
                    style={{ height: "40px", width: "40px" }}
                  />
                </span>
                <span className="navbar-toggler-toggled">
                  <img
                    src={"/svg/closed.svg"}
                    alt={translate(DictionaryEnum.COMMON_CLOSE_MENU)}
                    style={{ height: "40px", width: "40px" }}
                  />
                </span>
              </button>
              {/* <!-- End Responsive Toggle Button --> */}
            </nav>
            {/* <!-- End Nav --> */}
          </div>
        </div>
      </header>
    )
  }
}

export default withLanguageRegion(MobileHeader)
