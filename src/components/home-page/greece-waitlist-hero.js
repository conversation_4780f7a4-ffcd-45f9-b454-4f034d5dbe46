import React from "react"
import Slider from "react-slick"
import LocaleEnum from "../../configs/locale-config"
import withLanguageRegion from "../../hoc/withLanguageRegion"
import { DictionaryEnum } from "../../translations/dictionary-entries"
import DownloadAppStarsCta from "./download-app-stars-cta"
import DownloadAppBtn from "../download-app-btn"

const SELECTED_DURATION = 5000 // Adjust the duration as needed

const IMAGE_CONFIG = {
  [LocaleEnum.UK]: [
    "/img/hero/investments-mock-uk.png",
    "/img/hero/nvidia-mock.png",
    "/img/hero/learn-mock.png",
    "/img/hero/automation-mock-uk.png",
    "/img/hero/accounts-mock-uk.png",
  ],
  [LocaleEnum.EU]: [
    "/img/hero/investments-mock-eu.png",
    "/img/hero/nvidia-mock.png",
    "/img/hero/learn-mock.png",
    "/img/hero/automation-mock-eu.png",
    "/img/hero/accounts-mock-eu.png",
  ],
}

class GreeceWaitlistHeroSection extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showModal: false,
    }
    this.desktopSliderRef = React.createRef()
    this.mobileSliderRef = React.createRef()
  }

  handleOpenModal = () => {
    this.setState({ showModal: true })
  }

  handleCloseModal = () => {
    this.setState({ showModal: false })
  }

  render() {
    const {
      savingsProductMaxInterest,
      className,
      locale,
      urlPrefix,
      translate,
    } = this.props
    const { showModal } = this.state
    const images = IMAGE_CONFIG[locale]

    const desktopSettings = {
      infinite: true,
      speed: 1000,
      slidesToShow: 3,
      initialSlide: 1,
      slidesToScroll: 1,
      autoplaySpeed: SELECTED_DURATION,
      centerMode: true,
      isVariableWidth: false,
      adaptiveHeight: true,
      dots: false,
      arrows: false,
      centerPadding: "0px",

      ref: this.desktopSliderRef,
    }

    const mobileSettings = {
      infinite: true,
      speed: 1000,
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplaySpeed: SELECTED_DURATION,
      centerMode: true,
      isVariableWidth: false,
      adaptiveHeight: true,
      dots: false,
      arrows: false,
      centerPadding: "0px",

      ref: this.mobileSliderRef,
    }

    return (
      <section
        className={`noto-sans tw-relative tw-bg-white tw-overflow-x-hidden tw-overflow-y-hidden ${
          className ?? ""
        }`}
      >
        <img
          className="tw-hidden lg:tw-block tw-absolute tw-object-none tw-top-0 tw-left-1/2 tw-transform tw-translate-x-[-50%] tw-max-w-fit"
          src={"/img/hero-desktop-bg.webp"}
          alt="Download on the App Store"
          loading="lazy"
        />
        <img
          className="lg:tw-hidden tw-absolute tw-object-none tw-top-0 tw-left-1/2 tw-transform tw-translate-x-[-50%] tw-max-w-fit"
          src={"/img/hero-mobile-bg.webp"}
          alt="Download on the App Store"
          loading="lazy"
        />

        <div className="lg:tw-container tw-pt-16 lg:tw-pt-40 tw-mx-auto tw-relative ">
          <div className="tw-flex tw-flex-col lg:tw-flex-row tw-gap-5 sm:tw-gap-16 tw-justify-center lg:tw-justify-start tw-flex-grow-0 tw-flex-shrink-0 ">
            <div className="tw-px-5 lg:tw-px-0 tw-flex tw-flex-col lg:tw-basis-1/2 tw-flex-shrink-0 lg:tw-pr-8">
              <div className="tw-mb-8 tw-font-bold !tw-text-[2.5rem] lg:!tw-text-[3.75rem] !tw-leading-[120%] tw-w-full">
                <h2>{translate(DictionaryEnum.GREECE_WAITLIST_PAGE_TITLE)}</h2>
                <div className="animated-text-container tw-w-full">
                  <h2
                    className="tw-pb tw-relative tw-w-full"
                    style={{
                      background:
                        "linear-gradient(270deg, #536AE3 55.5%, #DD2663 122.84%)",
                      backgroundClip: "text",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      color: "transparent",
                    }}
                  >
                    {translate(DictionaryEnum.GREECE_WAITLIST_PAGE_SUBTITLE)}
                  </h2>
                </div>
              </div>
              <p
                className="tw-mb-14 noto-sans tw-text-lg lg:tw-text-2xl tw-leading-9 tw-text-neutralPrimary tw-font-normal"
                dangerouslySetInnerHTML={{
                  __html: translate(
                    DictionaryEnum.GREECE_WAITLIST_PAGE_DESCRIPTION_HTML,
                    {
                      interestRate: savingsProductMaxInterest,
                      urlPrefix,
                    }
                  ),
                }}
              />
              <div className="tw-mb-4 lg:tw-flex tw-flex-col tw-items-start">
                <DownloadAppBtn />
              </div>
              <p className="tw-mb-12 tw-text-lg tw-text-neutralPrimary noto-sans tw-text-left tw-mt-[6px]">
                {translate(DictionaryEnum.CAPITAL_AT_RISK)}{" "}
                <span
                  dangerouslySetInnerHTML={{
                    __html: translate(DictionaryEnum.OTHER_CHARGES_MAY_APPLY, {
                      urlPrefix,
                    }),
                  }}
                />
              </p>
              <DownloadAppStarsCta />
            </div>
            <div className="lg:tw-basis-1/2 tw-z-10 sm:tw-w-[880px]">
              <div className="sm:tw-hidden image-carousel-container">
                <Slider ref={this.mobileSliderRef} {...mobileSettings}>
                  {images.map((image, index) => (
                    <div key={index}>
                      <img
                        height={564}
                        width={277}
                        src={image}
                        alt={`${index}`}
                        className="tw-mx-auto tw-h-[564px] tw-w-[277]"
                      />
                    </div>
                  ))}
                </Slider>
              </div>
              <div className="tw-hidden sm:tw-block image-carousel-container">
                <Slider ref={this.desktopSliderRef} {...desktopSettings}>
                  {images.map((image, index) => (
                    <div key={index}>
                      <img
                        height={564}
                        width={277}
                        src={image}
                        alt={`${index}`}
                        className="tw-mx-auto tw-h-[564px] tw-w-[277]"
                      />
                    </div>
                  ))}
                </Slider>
              </div>
            </div>
          </div>
        </div>
        <div
          className="tw-absolute tw-bottom-0 tw-w-full"
          style={{
            height: "42px",
            backgroundColor: "#F1F3FD",
          }}
        />
      </section>
    )
  }
}

export default withLanguageRegion(GreeceWaitlistHeroSection)
