import React from "react"
import BlogCategoryTag from "./blog-category-tag"
import { Link } from "gatsby"
import { formatDateToLongFormWithoutComma } from "../../utils/date"
class BlogPostPreviewVerticalCard extends React.Component {
  #navigateToArticle = (blogPostItem, locale) => {
    window.location.href =
      `/${locale}/blog/${blogPostItem.category.slug}/${blogPostItem.slug}`.toLowerCase()
  }
  render() {
    const { item, className, locale } = this.props
    return (
      <div
        className={`d-flex flex-column gap-3 blog-font-family ${
          className ?? ""
        }`}
      >
        <button
          className="blog-post-preview-image-container blog-border-radious d-flex align-items-center justify-content-center cursor-pointer"
          onClick={() => this.#navigateToArticle(item, locale)}
        >
          <img className="blog-post-preview-image" src={item.image.file.url} />
          <BlogCategoryTag
            locale={locale}
            slug={item.category.slug}
            name={item.category.name}
          />
        </button>
        <div id="content" className="d-flex flex-column gap-3">
          <Link
            className="cursor-pointer no-underline-link"
            to={`/${locale}/blog/${item.category.slug}/${item.slug}/`.toLowerCase()}
          >
            <h5
              className="fw-bolder blog-text-dark text-big text-up-to-three-lines m-0"
              style={{ fontSize: "1.25rem" }}
            >
              {item.title}
            </h5>
          </Link>
          <p
            className="m-0 text-up-to-five-lines"
            style={{ lineHeight: "1.375rem" }}
          >
            {item.metaDescription}
          </p>
          <div
            className="d-flex align-items-center"
            style={{ fontSize: "0.875rem" }}
          >
            <div className="d-flex gap-2 align-items-center me-3">
              <span className="">
                {formatDateToLongFormWithoutComma(item.date, locale)}
              </span>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default BlogPostPreviewVerticalCard
