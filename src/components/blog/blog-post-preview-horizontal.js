import React from "react"
import Blog<PERSON>ategoryTag from "./blog-category-tag"
import { <PERSON> } from "gatsby"
import { DictionaryMappingEnum } from "../../translations/translation-entries"
import { translate } from "../../translations/translator"
import { formatDateToLongFormWithoutComma } from "../../utils/date"

class BlogPostPreviewHorizontal extends React.Component {
  #navigateToArticle = (blogPostItem, locale) => {
    window.location.href =
      `/${locale}/blog/${blogPostItem.category.slug}/${blogPostItem.slug}`.toLowerCase()
  }
  render() {
    const { item, className, locale } = this.props

    return (
      <div
        className="blog-font-family p-lg-7 p-md-5 p-3 blog-border-radious"
        style={{ backgroundColor: "#f6f8fd" }}
      >
        <div
          className={`row flex-column-reverse flex-lg-row align-items-center ${
            className ?? ""
          }`}
        >
          <div
            id="content"
            className="d-flex flex-column gap-4 col-12 col-lg-8"
          >
            <a
              className="cursor-pointer no-underline-link"
              href={`/${locale}/blog/${item.category.slug}/${item.slug}`.toLowerCase()}
            >
              <h5 className="fw-bolder blog-text-dark text-up-to-three-lines m-0 blog-post-preview-horizontal-header">
                {item.title}
              </h5>
            </a>
            {item.metaDescription && (
              <p
                className="m-0 text-big text-up-to-five-lines"
                style={{ fontSize: "1.125rem", lineHeight: "1.75rem" }}
              >
                {item.metaDescription}
              </p>
            )}
            <div className="d-flex align-items-center">
              <div className="d-flex gap-2 align-items-center me-3 text-medium">
                <span className="blog-text-dark">
                  {formatDateToLongFormWithoutComma(item.date, locale)}
                </span>
              </div>
            </div>
            <Link
              className="cta cta-button btn button-medium text-medium btn-primary bg-dark-blue"
              to={`/${locale}/blog/${item.category.slug}/${item.slug}/`.toLowerCase()}
            >
              {translate(DictionaryMappingEnum.READ_POST, locale)}
            </Link>
          </div>
          <button
            className="cursor-pointer col-12 col-lg-4 col-lg-4 pb-4"
            onClick={() => this.#navigateToArticle(item, locale)}
          >
            <div className="blog-post-preview-image-container">
              <img
                className="blog-post-preview-image"
                alt="post preview image"
                src={item.image.file.url}
              />
              <BlogCategoryTag
                name={item.category.name}
                slug={item.category.slug}
                locale={locale}
              />
            </div>
          </button>
        </div>
      </div>
    )
  }
}

export default BlogPostPreviewHorizontal
