import React from "react"
import BlogPostPreviewHorizontal from "./blog-post-preview-horizontal"
import BlogMultiplePostPreview from "./blog-multiple-post-preview"
import { Link } from "gatsby"
import { DictionaryMappingEnum } from "../../translations/translation-entries"
import { translate } from "../../translations/translator"

class BlogPostCategoryPreview extends React.Component {
  render() {
    const { blogPosts, categoryName, categorySlug, className, locale } =
      this.props

    // Split the first post and the next three posts
    const prominentPost = blogPosts[0]
    const otherPosts = blogPosts.slice(1, 7) // This will get posts 2 to 7

    return (
      <div
        className={`category-post-container d-flex flex-column ${
          className ?? ""
        }`}
      >
        <h2
          className="text-big blog-text-dark fw-bolder m-0"
          style={{ fontSize: "2.5rem" }}
        >
          {categoryName}
        </h2>
        <BlogPostPreviewHorizontal
          locale={locale}
          item={prominentPost}
          hideImage={false}
        />

        <BlogMultiplePostPreview
          locale={locale}
          items={otherPosts}
          itemsPerRow={3}
        />
        <div className="text-center">
          <Link
            className="cta cta-button btn button-medium text-medium-no-font-family btn-primary bg-dark-blue"
            to={`/${locale}/blog/${categorySlug}/`}
          >
            {translate(DictionaryMappingEnum.VIEW_ALL_POSTS, locale)}
          </Link>
        </div>
      </div>
    )
  }
}

export default BlogPostCategoryPreview
