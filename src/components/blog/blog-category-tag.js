import React from "react"
import { <PERSON> } from "gatsby"

class BlogCategoryTag extends React.Component {
  render() {
    const { name, slug, className, locale } = this.props

    return (
      <Link
        className="cursor-pointer no-underline-link fw-bold text-light-blue blog-category-tag-container"
        to={`/${locale}/blog/${slug}/`}
      >
        {name}
      </Link>
    )
  }
}

export default BlogCategoryTag
