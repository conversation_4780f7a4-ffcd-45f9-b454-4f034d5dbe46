import React from "react"
import Header from "./header"
import Footer from "./footer"
import Main from "./main"
import QRCodeModal from "./qr-code-modal"
import MobileBottomBanner from "./mobile-bottom-banner"
import { customAlphabet } from "nanoid"
import { FOOTER_CONFIG } from "../configs/footer-config"
import withLanguageRegion from "../hoc/withLanguageRegion"
import GreeceWaitlistModal from "./greece-waitlist-modal"

const alphabet = "0123456789abcdefghijklmnopqrstuvwxyz"
const nanoid = customAlphabet(alphabet)

class Layout extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showQRCodeModal: false,
      showGreeceWaitlistModal: false,
    }
  }

  #generateAnonymousId = () => {
    if (
      window &&
      window.cookieManager &&
      typeof window.cookieManager.get === "function"
    ) {
      let anonymousId = window.cookieManager.get("anonymousId")
      if (!anonymousId) {
        anonymousId = nanoid()
        window.cookieManager.set("anonymousId", anonymousId)
      }
      return anonymousId
    }
  }

  #setPageUserLanded = () => {
    if (
      window &&
      window.cookieManager &&
      typeof window.cookieManager.get === "function"
    ) {
      let pageUserLanded = window.cookieManager.get("pageUserLanded")
      if (!pageUserLanded) {
        pageUserLanded = window.location.pathname
        window.cookieManager.set("pageUserLanded", pageUserLanded)
      }
    }
  }

  #setGoogleUserId = (userId) => {
    console.log("layout.setGoogleUserId - " + userId)
    if (window && window.gtag) {
      console.log("layout.setGoogleUserId - setting user id")
      window.gtag("set", "user_id", userId)
    } else {
      if (!window) {
        console.log("layout.setGoogleUserId - window does not exist")
      } else if (!window.gtag) {
        console.log("layout.setGoogleUserId - gtag does not exist")
      }
    }
  }

  #setQrCodeModalShow = (show) => {
    this.setState({ showQRCodeModal: show })
  }
  #setGreeceWaitlistModalShow = (show) => {
    this.setState({ showGreeceWaitlistModal: show })
  }

  componentDidMount() {
    window.eventEmitter.on("DISPLAY_QR_CODE_MODAL", () => {
      this.#setQrCodeModalShow(true)
    })
    window.eventEmitter.on("DISPLAY_GREECE_WAITLIST_MODAL", () => {
      this.#setGreeceWaitlistModalShow(true)
    })

    const anonymousId = this.#generateAnonymousId()
    this.#setGoogleUserId(anonymousId)

    this.#setPageUserLanded()

    window.datadogLogs &&
      window.datadogLogs.logger.info(
        `Page loaded + ${window.location.pathname}`,
        {
          location: window.location.href,
          page: window.location.pathname,
        }
      )

    // redirects to reward page
    if (window.location.pathname === "/") {
      const params = new window.URLSearchParams(window.location.search)
      const grsfId = params.get("grsf")
      const wlthdId = params.get("wlthd")

      if (grsfId) {
        window.location.href = `/reward?grsf=${grsfId}`
      } else if (wlthdId) {
        window.location.href = `/reward?wlthd=${wlthdId}`
      }
    }
  }

  render() {
    const {
      activePage,
      children,
      headerBackgroundImg,
      backgroundColor,
      headerBackgroundColor,
      isHeaderAbsolutePositioned,
      className,
      translate,
    } = this.props
    const { showQRCodeModal, showGreeceWaitlistModal } = this.state

    const defaultBackgroundColor = "#F6F8FD"

    return (
      <div
        style={{
          backgroundColor: `${backgroundColor ?? defaultBackgroundColor}`,
        }}
        className={`${className ?? ""} tw-pt-10`}
      >
        <Header
          activePage={activePage}
          backgroundImg={headerBackgroundImg}
          backgroundColor={headerBackgroundColor}
          isAbsolutePositioned={isHeaderAbsolutePositioned}
        />
        <Main>{children}</Main>
        <Footer
          includeCta={FOOTER_CONFIG[activePage]?.includeCta ?? false}
          ctaColorTheme={FOOTER_CONFIG[activePage]?.colorTheme ?? "dark"}
          ctaTitle={
            translate(FOOTER_CONFIG[activePage]?.ctaTitle) ??
            "Flexible, personalised and automated."
          }
          ctaSubtitle={translate(FOOTER_CONFIG[activePage]?.ctaSubtitle)}
          isIndex={activePage === "index"}
        />
        <MobileBottomBanner />
        <QRCodeModal
          handleClose={() => this.#setQrCodeModalShow(false)}
          show={showQRCodeModal}
        />
        <GreeceWaitlistModal
          handleClose={() => this.#setGreeceWaitlistModalShow(false)}
          show={showGreeceWaitlistModal}
        />
        {/* Netlify form for Greece waitlist workaround */}
        <form hidden data-netlify="true" name="greece-waitlist-email-form">
          <input name="email" />
        </form>
      </div>
    )
  }
}

export default withLanguageRegion(Layout)
