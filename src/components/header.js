import { <PERSON> } from "gatsby"
import React from "react"
import { <PERSON> as ScrollLink } from "react-scroll"
import AdminHeader from "./admin-header"
import ReferralHeader from "./referral-header"
import MobileHeader from "./mobile-header"
import LanguagePicker from "./language-picker"
import { ensureTrailingSlash } from "../utils/stringUtil"
import { HEADER_LINKS } from "../configs/links-config"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { DictionaryEnum } from "../translations/dictionary-entries"
import { shouldDisplayLanguagePicker } from "../utils/languageUtils"
import AnnouncementBanner from "./announcement-banner"
import MobileAnnouncementBanner from "./mobile-announcement-banner"

const largeGridPages = ["index", "affiliates", "asset-page", "greece-waitlist"]

class Header extends React.Component {
  #translatedVersionExistsForPage = () => {
    if (typeof window !== "undefined") {
      return (
        !window.location.href.includes("glossary") &&
        !window.location.href.includes("stocks") &&
        !window.location.href.includes("etfs") &&
        !window.location.href.includes("blog")
      )
    }
    return false
  }

  #displayQRCodeModal = () => {
    window.eventEmitter.emit("DISPLAY_QR_CODE_MODAL")
  }

  #renderNavLinksListItems = (activePage) => {
    const { translate, urlPrefix } = this.props

    return HEADER_LINKS.map((linkItem) => {
      if (
        linkItem.type === "scroll" &&
        activePage === linkItem.pageToBeScrollable
      ) {
        return (
          <li key={linkItem.slug} className="navbar-nav-item">
            <ScrollLink
              className={`noto-sans tw-text-base tw-py-0 !tw-font-normal hover:!tw-font-normal tw-text-[#10132799] tw-px-3 hover:!tw-no-underline hover:!tw-text-primary tw-mix-blend-multiply ${
                ensureTrailingSlash(activePage || "") ===
                  ensureTrailingSlash(linkItem.slug || "") ||
                linkItem.slugsToBeActive?.includes(activePage)
                  ? "text-light-blue !tw-py-[6px] tw-rounded-full tw-bg-gray "
                  : ""
              }`}
              to={linkItem.domID}
              spy={true}
              smooth={true}
              offset={70}
              duration={500}
              style={{ cursor: "pointer" }}
            >
              {translate(linkItem.label)}
            </ScrollLink>
          </li>
        )
      } else {
        return (
          <li key={linkItem.slug} className="navbar-nav-item">
            <Link
              className={`noto-sans tw-text-base !tw-font-normal hover:!tw-font-normal tw-text-[#10132799] tw-py-0 tw-px-3 hover:!tw-no-underline hover:!tw-text-primary tw-mix-blend-multiply ${
                ensureTrailingSlash(activePage || "") ===
                  ensureTrailingSlash(linkItem.slug || "") ||
                linkItem.slugsToBeActive?.includes(activePage)
                  ? "text-light-blue !tw-py-[6px] tw-rounded-full tw-bg-gray"
                  : ""
              }`}
              to={`/${urlPrefix}/${linkItem.slug}`}
            >
              {translate(linkItem.label)}
            </Link>
          </li>
        )
      }
    })
  }

  #renderHeaderButtons = () => {
    const { translate, region, urlPrefix } = this.props

    return (
      <>
        <li className="navbar-nav-item pr-3 mr-2 noto-sans tw-font-semibold lg:tw-ml-3">
          <button
            id="get-the-app-header"
            type="submit"
            className={
              "hover:tw-bg-opacity-90 tw-px-5 tw-py-3 tw-bg-neutralPrimary tw-text-white tw-rounded-3xl tw-text-base !tw-leading-[18px] tw-font-semibold tw-cursor-pointer tw-flex tw-items-center tw-justify-center"
            }
            onClick={() => this.#displayQRCodeModal()}
          >
            {translate(DictionaryEnum.COMMON_NAV_DOWNLOAD_APP)}
          </button>
        </li>
        <li className="navbar-nav-item">
          <a
            href={"https://app.wealthyhood.com/auth"}
            id="login-header"
            className={
              "hover:tw-bg-opacity-90 tw-px-5 tw-py-3 tw-bg-primary tw-text-white tw-rounded-3xl tw-text-base !tw-leading-[18px] tw-font-semibold tw-cursor-pointer tw-flex tw-items-center tw-justify-center hover:tw-no-underline hover:tw-text-white tw-gap-1"
            }
          >
            <span>{translate(DictionaryEnum.COMMON_NAV_LOGIN)}</span>
            <span>
              <svg
                width="16"
                height="11"
                viewBox="0 0 14 13"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="m-0 p-0 align-middle"
              >
                <path
                  d="M7.23793 12.2365L6.08736 11.0987L10.0824 7.10369H0.046875V5.44176H10.0824L6.08736 1.45312L7.23793 0.308948L13.2017 6.27273L7.23793 12.2365Z"
                  fill="#fff"
                />
              </svg>
            </span>
          </a>
        </li>
        {/* Temporarily hide the language picker on the non-translated pages - until we release the greek version */}
        {this.#translatedVersionExistsForPage() &&
          shouldDisplayLanguagePicker(urlPrefix, region) && (
            <li className="navbar-nav-item">
              <LanguagePicker className="tw-ml-3" />
            </li>
          )}
      </>
    )
  }

  render() {
    const {
      activePage,
      backgroundImg,
      backgroundColor,
      isAbsolutePositioned,
      urlPrefix,
    } = this.props

    if (
      activePage === "index" ||
      activePage === "affiliates" ||
      activePage === "blog" ||
      activePage === "post" ||
      activePage === "greece-waitlist"
    ) {
      return (
        <>
          {/* <!-- Mobile Navigation & Banner --> */}
          <div className="d-lg-none">
            <MobileAnnouncementBanner />
            <MobileHeader
              activePage={activePage}
              backgroundColor={backgroundColor}
              isAbsolutePositioned={isAbsolutePositioned}
            />
          </div>
          {/* <!-- End Mobile Navigation & Banner --> */}

          {/* Desktop Banner - Rendered separately for clarity, using d-none d-lg-block */}
          <div className="d-none d-lg-block">
            <AnnouncementBanner />
          </div>

          <header
            id="header"
            className={`header header-bg-transparent d-none d-lg-block ${
              isAbsolutePositioned ? "tw-absolute" : ""
            }`}
            style={{
              backgroundColor: backgroundColor,
            }}
          >
            <div className="header-section header-bg-transparent pt-4">
              <div
                id="logoAndNav"
                className={`header-bg-transparent ${
                  largeGridPages.includes(activePage)
                    ? "tw-container mx-auto"
                    : "container px-5"
                }`}
              >
                {/* <!-- Nav --> */}
                <nav className="navbar navbar-expand-lg pt-5">
                  {/* <!-- Logo --> */}
                  <Link
                    className="navbar-brand py-0"
                    to={`/${urlPrefix}/`}
                    aria-label="Wealthyhood"
                    style={{ width: "13rem" }}
                  >
                    <img
                      src={"/svg/logo-full-dark.svg"}
                      style={{ height: "35px", width: "208px" }}
                      alt="Wealthyhood Logo"
                    />
                  </Link>
                  {/* <!-- End Logo --> */}

                  {/* <!-- Big Screen Navigation --> */}
                  <div
                    id="navBar"
                    className="collapse navbar-collapse d-none d-lg-block"
                  >
                    <ul className="navbar-nav">
                      {this.#renderNavLinksListItems(activePage)}
                      {this.#renderHeaderButtons()}
                    </ul>
                  </div>
                  {/* <!-- End Big Screen Navigation --> */}
                </nav>
                {/* <!-- End Nav --> */}
              </div>
            </div>
          </header>
        </>
      )
    } else if (
      activePage === "affiliate-dashboard" ||
      activePage === "referral-dashboard"
    ) {
      // In the referral/affiliate dashboards, we want to remove the navigation options from the navbar.
      return <ReferralHeader />
    } else if (activePage === "manage-ambassadors") {
      // If in manage-ambassadors, we want to return the admin header (with a Netlify login button)
      return <AdminHeader />
    } else {
      return (
        <>
          {backgroundImg ? (
            <div
              className="w-100 position-absolute"
              style={{
                background: `url(${backgroundImg}) center center`,
                height: "600px",
              }}
            />
          ) : (
            <img
              src={"/img/header-background.png"}
              className="w-100 position-absolute z-index-0"
              alt={"Header Background"}
            />
          )}

          {/* <!-- Mobile Navigation & Banner --> */}
          <div className="d-lg-none">
            <MobileAnnouncementBanner />
            <MobileHeader
              activePage={activePage}
              backgroundColor={backgroundColor}
            />
          </div>
          {/* <!-- End Mobile Navigation & Banner --> */}

          {/* Desktop Banner */}
          <div className="d-none d-lg-block">
            <AnnouncementBanner />
          </div>

          <header
            id="header"
            className={`header header-bg-transparent d-none d-lg-block ${
              isAbsolutePositioned ? "tw-absolute" : ""
            }`}
            style={{
              backgroundColor: backgroundColor,
            }}
          >
            <div className="header-section header-bg-transparent pt-4">
              <div
                id="logoAndNav"
                className={`bg-white py-5 ${
                  largeGridPages.includes(activePage)
                    ? "tw-container mx-auto "
                    : "container px-5"
                }`}
                style={{
                  borderRadius: "16px",
                  boxShadow: "0px 12px 32px rgba(68, 65, 211, 0.08)",
                }}
              >
                {/* <!-- Nav --> */}
                <nav className="navbar navbar-expand-lg">
                  {/* <!-- Logo --> */}
                  <Link
                    className="navbar-brand py-0"
                    to={`/${urlPrefix}/`}
                    aria-label="Wealthyhood"
                    style={{ width: "13rem" }}
                  >
                    <img
                      src={"/svg/logo-full-dark.svg"}
                      style={{ height: "35px", width: "208px" }}
                      alt="Wealthyhood Logo"
                    />
                  </Link>
                  {/* <!-- End Logo --> */}

                  {/* <!-- Navigation --> */}
                  <div id="navBar" className="collapse navbar-collapse">
                    <ul className="navbar-nav">
                      {this.#renderNavLinksListItems(activePage)}
                      {this.#renderHeaderButtons()}
                    </ul>
                  </div>
                  {/* <!-- End Navigation --> */}
                </nav>
                {/* <!-- End Nav --> */}
              </div>
            </div>
          </header>
        </>
      )
    }
  }
}

export default withLanguageRegion(Header)
