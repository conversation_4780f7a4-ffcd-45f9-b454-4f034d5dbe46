/**
 * Seo component that queries for data with
 *  Gatsby's useStaticQuery React hook
 *
 * See: https://www.gatsbyjs.org/docs/use-static-query/
 */
import React from "react"
import PropTypes from "prop-types"
import { Helmet } from "react-helmet"
import { useStaticQuery, graphql } from "gatsby"
import withLanguageRegion from "../hoc/withLanguageRegion"
import LocaleEnum from "../configs/locale-config"
import { LanguageEnum } from "../configs/language-region-config"
import { usePathname } from "../contexts/PathnameContext"

// Private helper functions for the Seo component
const _getMetaImage = (image, isDynamicImage, defaultImage) => {
  if (image) {
    return isDynamicImage ? image : `https://wealthyhood.com${image}`
  }
  return defaultImage
}

const _getFullUrl = (baseUrl, path) => {
  const url = new URL(path, baseUrl).toString()
  return url.endsWith("/") ? url : `${url}/`
}

const _getCanonicalUrlPath = (pathname = "/") => {
  const pathSegments = pathname.split("/").filter(Boolean)
  const firstUrlSegment = pathSegments[0] || ""
  // Extract the page path (everything after the first segment)
  const pagePath = pathSegments.slice(1).join("/")
  let canonicalPrefix = ""
  // Handle different cases based on the first segment
  if (firstUrlSegment === LocaleEnum.UK || firstUrlSegment === LocaleEnum.EU) {
    // If segment is a locale (uk or eu), canonical is eu
    canonicalPrefix = LocaleEnum.EU
  } else if (
    firstUrlSegment === LanguageEnum.English ||
    firstUrlSegment === LanguageEnum.Greek
  ) {
    // If segment is a language code only (en or el), use that language
    canonicalPrefix = firstUrlSegment
  } else if (firstUrlSegment.match(/^[a-z]{2}-[a-z]{2}$/)) {
    // If segment is in language-region format
    if (firstUrlSegment === "el-gr") {
      // For el-gr, canonical is el-gr
      canonicalPrefix = "el-gr"
    } else if (firstUrlSegment === "en-gr") {
      // For en-gr, canonical is eu
      canonicalPrefix = LocaleEnum.EU
    }
  }

  // Fix for root page to avoid double slashes
  return pagePath ? `/${canonicalPrefix}/${pagePath}/` : `/${canonicalPrefix}/`
}

/**
 * @description This function determines if the current page should be indexed by comparing the path with the canonical path.
 * @param {string} pathname - The current pathname
 * @returns {boolean} True if the page should be indexed, false otherwise.
 */
const _shouldIndex = (pathname = "/") => {
  if (
    pathname.includes("reward") ||
    pathname.includes("affiliates") ||
    pathname.includes("affiliate-dashboard")
  ) {
    return false
  }

  // Get the normalized current path (with trailing slash)
  const normalizedCurrentPath = pathname.endsWith("/")
    ? pathname
    : `${pathname}/`

  // Get the canonical path using the new implementation
  const canonicalPath = _getCanonicalUrlPath(pathname)

  // The page should be indexed only if the current path matches the canonical path
  return normalizedCurrentPath === canonicalPath
}

/**
 * @description This function generates hreflang configurations. Config is the same for the following categories:
 * - url prefix is language-region pairs or EU locale
 * - url prefix is language code only
 *
 * @param {string} pathname - The current pathname
 * @returns {Array} Array of objects with languageCode and url for each hreflang variant.
 */
const _generateHrefLangConfig = (pathname = "/") => {
  // Extract path segments from pathname
  const pathSegments = pathname.split("/").filter(Boolean)

  const firstUrlSegment = pathSegments[0] || ""
  // Everything after the first segment
  const pagePath =
    pathSegments.length > 0 ? pathSegments.slice(1).join("/") : ""

  // Check if the firstUrlSegment is a language-region pair or locale
  const isLanguageRegionOrLocale =
    firstUrlSegment === LocaleEnum.EU ||
    firstUrlSegment === LocaleEnum.UK || // Add UK check
    firstUrlSegment.match(/^[a-z]{2}-[a-z]{2}$/i)

  // Check if the firstUrlSegment is a language code only
  const isLanguageOnly =
    firstUrlSegment === LanguageEnum.English ||
    firstUrlSegment === LanguageEnum.Greek

  let hrefLangConfig = []

  // Generate hreflang links for language-region or locale pages
  if (isLanguageRegionOrLocale) {
    // Define the hreflang configs with just the language codes and prefixes
    hrefLangConfig = [
      { languageCode: "en-gr", prefix: "en-gr" },
      { languageCode: "el-gr", prefix: "el-gr" },
      { languageCode: "x-default", prefix: LocaleEnum.EU },
    ]
  }
  // Generate hreflang links for language-only pages
  else if (isLanguageOnly) {
    hrefLangConfig = [
      { languageCode: LanguageEnum.Greek, prefix: LanguageEnum.Greek },
      { languageCode: "x-default", prefix: LanguageEnum.English },
    ]
  }

  // Map through the configs to add the URLs using the createUrl function
  return hrefLangConfig.map(({ languageCode, prefix }) => ({
    languageCode,
    url: pagePath ? `/${prefix}/${pagePath}/` : `/${prefix}/`,
  }))
}

function Seo({
  description = "",
  lang = "en",
  title,
  hrefLangs: providedHrefLangs,
  image,
  isDynamicImage,
  schemaMarkup,
  schemaFaqsMarkup,
}) {
  // Use the usePathname hook to get the pathname from context
  const pathname = usePathname() || "/"

  const { site } = useStaticQuery(graphql`
    query {
      site {
        siteMetadata {
          title
          description
          author
          image
          siteUrl
          social {
            twitter
            image
          }
        }
      }
    }
  `)

  const metaDescription = description || site.siteMetadata.description
  const metaTitle = title || site.siteMetadata.title
  const metaImage = _getMetaImage(
    image,
    isDynamicImage,
    site.siteMetadata.image
  )

  // Use provided hrefLangs or generate them (for backward compatibility with blog pages)
  const hrefLangs = providedHrefLangs || _generateHrefLangConfig(pathname)

  const canonicalUrlPath = _getCanonicalUrlPath(pathname)
  const shouldIndexPage = _shouldIndex(pathname)

  return (
    <Helmet>
      <html lang={lang} amp />
      <title itemProp="name" lang={lang}>
        {metaTitle}
      </title>
      <meta name="description" content={metaDescription} />

      {/* Canonical and href lang links */}
      <link
        rel="canonical"
        href={_getFullUrl(site.siteMetadata.siteUrl, canonicalUrlPath)}
      />

      {/* Alternate hreflang links */}
      {hrefLangs.map((hrefLang, index) => (
        <link
          key={index}
          rel="alternate"
          hreflang={hrefLang.languageCode}
          href={_getFullUrl(site.siteMetadata.siteUrl, hrefLang.url)}
        />
      ))}
      {/* Canonical and href lang links */}

      {/* Open Graph Tags */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={metaTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={metaImage} />
      <meta
        property="og:url"
        content={_getFullUrl(site.siteMetadata.siteUrl, pathname)}
      />
      <meta property="og:locale" content="en_GB" />
      {/* End Open Graph Tags */}

      {/* Twitter OG tags */}
      <meta property="twitter:card" content="summary_large_card" />
      <meta
        property="twitter:site"
        content={site.siteMetadata.social.twitter}
      />
      <meta property="twitter:image" content={metaImage} />
      <meta property="twitter:title" content={metaTitle} />
      <meta property="twitter:description" content={metaDescription} />
      {/* End Twitter OG tags */}

      {schemaMarkup && (
        <script type="application/ld+json">
          {JSON.stringify(schemaMarkup)}
        </script>
      )}
      {schemaFaqsMarkup && (
        <script type="application/ld+json">
          {JSON.stringify(schemaFaqsMarkup)}
        </script>
      )}
      {!shouldIndexPage && <meta name="robots" content="noindex" />}
      {/* Custom Scripts */}
      <link
        rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,700,0,0"
      />
      <link
        rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,700,1,0"
      />
      {/* End Custom Scripts */}
    </Helmet>
  )
}

Seo.propTypes = {
  description: PropTypes.string,
  lang: PropTypes.string,
  meta: PropTypes.arrayOf(PropTypes.object),
  title: PropTypes.string.isRequired,
  image: PropTypes.string,
  isDynamicImage: PropTypes.bool,
  schemaMarkup: PropTypes.object,
  schemaFaqsMarkup: PropTypes.object,
  hrefLangs: PropTypes.array, // Add hrefLangs to propTypes
}

export default withLanguageRegion(Seo)
