import React from "react"
import { DictionaryEnum } from "../translations/dictionary-entries"
import withLanguageRegion from "../hoc/withLanguageRegion"

const BTN_CONFIG = {
  dark: "btn-primary bg-dark-blue",
  light: "btn-light",
}

class GreeceWaitlistButton extends React.Component {
  #displayGreeceWaitlistModal = () => {
    window.eventEmitter.emit("DISPLAY_GREECE_WAITLIST_MODAL")
  }

  getButtonCss() {
    const { children, color } = this.props
    const btnColor = color || "dark"

    if (!children) {
      return `cta cta-button btn button-tall text-medium d-none d-lg-block ${BTN_CONFIG[btnColor]}`
    } else {
      return "btn btn-clean"
    }
  }

  render() {
    const { children, className, translate, displayOnMobile } = this.props
    const childrenIsImg = children?.type === "img"
    const childrenIsSpan = children?.type === "span"
    let buttonContent
    if (childrenIsSpan) {
      buttonContent = (
        <span className={children.props.className}>
          {translate(DictionaryEnum.GREECE_WAITLIST_PAGE_HERO_CTA)}
        </span>
      )
    } else if (childrenIsImg) {
      buttonContent = children
    } else {
      buttonContent = translate(DictionaryEnum.GREECE_WAITLIST_PAGE_HERO_CTA)
    }

    return (
      <>
        <button
          className={`${this.getButtonCss()} ${className ?? ""}`}
          onClick={() => this.#displayGreeceWaitlistModal()}
        >
          {buttonContent}
        </button>
        {displayOnMobile && (
          <>
            <button
              onClick={() => this.#displayGreeceWaitlistModal()}
              className="noto-sans cta cta-button btn btn-primary button-tall text-medium bg-dark-blue d-block d-lg-none"
            >
              {buttonContent}
            </button>
            <p className="tw-text-sm tw-text-neutralPrimary noto-sans tw-text-center tw-mt-[6px]">
              {translate(DictionaryEnum.CAPITAL_AT_RISK)}
            </p>
          </>
        )}
      </>
    )
  }
}

export default withLanguageRegion(GreeceWaitlistButton)
