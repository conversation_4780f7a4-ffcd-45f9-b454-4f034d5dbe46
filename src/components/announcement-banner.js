import React from "react"
import { <PERSON> } from "gatsby"
import withLanguageRegion from "../hoc/withLanguageRegion"
import { DictionaryEnum } from "../translations/dictionary-entries"
import { getLanguageFromLanguageRegion } from "../utils/languageUtils"

const AnnouncementBanner = ({ className = "", languageRegion, translate }) => {
  const language = getLanguageFromLanguageRegion(languageRegion)

  return (
    <div
      className={`${className}`}
      style={{
        height: "56px",
        width: "100%",
        background:
          "linear-gradient(90deg, #A4B2FF 0%, #536AE3 20%, #3A49B8 50%, #536AE3 80%, #A4B2FF 100%)",
        position: "fixed",
        top: 0,
        left: 0,
        zIndex: 1031,
      }}
    >
      <div
        style={{
          width: "100%",
          marginLeft: "auto",
          marginRight: "auto",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          paddingLeft: "1.5rem",
          paddingRight: "1.5rem",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "1.5rem",
          }}
        >
          <div
            className="noto-sans"
            style={{
              color: "#ffffff",
              fontWeight: 400,
              fontSize: "1.125rem",
              textAlign: "center",
            }}
          >
            {translate(DictionaryEnum.ANNOUNCEMENT_BANNER_TITLE)}
          </div>
          <Link
            to={`/${language}/press-releases/wealthyhood-raises-new-seed-round/`}
            className="noto-sans"
            style={{
              color: "#11152E",
              backgroundColor: "#ffffff",
              borderRadius: "9999px",
              paddingLeft: "1rem",
              paddingRight: "1rem",
              paddingTop: "0.5rem",
              paddingBottom: "0.5rem",
              fontWeight: 600,
              fontSize: "1rem",
              whiteSpace: "nowrap",
              textDecoration: "none",
              cursor: "pointer",
            }}
          >
            {translate(DictionaryEnum.ANNOUNCEMENT_BANNER_CTA)}
          </Link>
        </div>
      </div>
    </div>
  )
}

export default withLanguageRegion(AnnouncementBanner)
