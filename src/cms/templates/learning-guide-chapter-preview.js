import React from "react"
import LearningGuideChapterBody from "../../components/learning-guide/learning-guide-chapter-body"
import LearningGuideChapterHeader from "../../components/learning-guide/learning-guide-chapter-header"
import Layout from "../../components/layout"

class LearningGuideChapterPreview extends React.Component {
  render() {
    const { entry, widgetFor } = this.props
    const chapter = entry.getIn(["data", "chapter"])
    const title = entry.getIn(["data", "title"])

    return (
      <Layout>
        <div className="container space-top-2 space-bottom-3 w-lg-80">
          <LearningGuideChapterHeader chapterId={chapter} title={title} />
          <LearningGuideChapterBody content={widgetFor("body")} />
        </div>
      </Layout>
    )
  }
}

export default LearningGuideChapterPreview
