import appApiService, { API_ROUTES } from "../utils/appApiService"
const crypto = require("crypto")
const Analytics = require("analytics-node")
const postmark = require("postmark")

// postmark
const postmarkServerClient = new postmark.ServerClient(
  process.env.POSTMARK_SERVER_API_KEY,
  { useHttps: true }
)

// segment analytics
const analytics = new Analytics(process.env.SEGMENT_WRITE_KEY, { flushAt: 1 })

function _hashSHA256(str) {
  return crypto
    .createHash("sha256")
    .update(str)
    .digest("hex")
}

/**
 * Add new user to referral service
 */
async function addParticipant(participant) {
  const { email, influencerId, referrerId } = participant
  const participantDoc = await appApiService.postM2Madmin(
    API_ROUTES.participants.all(),
    {
      email,
      influencerId,
      referrerId,
    }
  )
  return participantDoc
}

async function getParticipantByGrsf(grsf) {
  const participants = await appApiService.getM2Madmin(
    API_ROUTES.participants.all(),
    {
      grsf,
    }
  )
  return participants[0]
}

/**
 * Tracks user through segment analytics
 */
async function trackUserSegment(user, utmParams) {
  const { email, referrerEmail } = user
  const { source, medium, campaign } = utmParams
  analytics.identify({
    anonymousId: _hashSHA256(email),
    traits: {
      email,
      referrerEmail,
      source,
      medium,
      campaign,
    },
    integrations: { All: true, MailChimp: true, Slack: false },
  })
  analytics.track({
    anonymousId: _hashSHA256(email),
    event: "Email Submitted",
    properties: {
      email,
      referrerEmail,
      source,
      medium,
      campaign,
    },
    // Mailchimp only access identify events
    integrations: { All: true, MailChimp: false, Slack: true },
  })
}

/**
 * Email sending methods
 */

function sendEmail(msg, emailType) {
  ;(async () => {
    try {
      await postmarkServerClient.sendEmailWithTemplate(msg)
      console.log(`[${emailType}] sent successfully!`)
    } catch (err) {
      console.log(`[${emailType}] to ${msg.to} failed to sent`)
      console.log(err)
    }
  })()
}

async function sendReferrerCreditEmail(referrerParticipant) {
  const referrerMail = referrerParticipant.email

  if (referrerParticipant.isAmbassador) {
    const msg = {
      From: "<NAME_EMAIL>",
      To: referrerMail,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: 19379642,
      TemplateModel: {
        grsf_email: referrerMail,
      },
    }

    sendEmail(msg, "Ambassador Referral Credit")
  }
}

exports.handler = async (event, context, callback) => {
  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    console.log("Forbidden non-POST request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  const body = JSON.parse(event.body)
  const { email, influencerId, referrerId, referrerGrsfId, utm } = body
  let participantData = {
    email,
    influencerId,
    referrerId,
  }
  if (referrerGrsfId) {
    // if referrerGrsfId exists, then fetch referrer participant in order to get the corresponding wlthd ID
    const referrerParticipant = await getParticipantByGrsf(referrerGrsfId)
    participantData.referrerId = referrerParticipant.wlthdId
  }

  let participant
  try {
    console.log(
      `adding user: ${email} | referred by: ${
        influencerId ? influencerId : participantData.referrerId
      } `
    )
    // Post user in referral service & track on Segment
    participant = await addParticipant(participantData)

    // referrer is populated at this point
    const referrer = participant.referrer
    const referrerEmail = referrer ? referrer.email : ""
    trackUserSegment({ email, referrerEmail }, utm)

    if (referrer) {
      sendReferrerCreditEmail(referrer)
    }
  } catch (err) {
    console.log("** error **")
    console.log(err)
  }

  if (participant) {
    console.log(JSON.stringify(participant, null, 4))
    return callback(null, {
      statusCode: 200,
      body: JSON.stringify(participant),
    })
  } else {
    return callback(null, {
      statusCode: 502,
    })
  }
}
