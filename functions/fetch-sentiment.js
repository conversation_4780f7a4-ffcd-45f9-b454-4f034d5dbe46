import stockNewsService from "../utils/stockNewsService"
import logger from "../utils/loggerService"

async function getSentiment(ticker) {
  try {
    const sentiment = await stockNewsService.getStockSentiment(
      ticker,
      "last30days"
    )

    return {
      sentiment,
    }
  } catch (err) {
    logger.error(
      `Error fetching sentiment for ticker: ${ticker}: ${err.message}`
    )
  }
}

exports.handler = async (event, context, callback) => {
  // Only allow GET requests
  if (event.httpMethod !== "GET") {
    console.log("Forbidden non-GET request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  const ticker = event.queryStringParameters.ticker

  try {
    const sentimentData = await getSentiment(ticker)
    return callback(null, {
      statusCode: 200,
      body: JSON.stringify(sentimentData),
    })
  } catch (err) {
    return callback(err, {
      statusCode: 502,
    })
  }
}
