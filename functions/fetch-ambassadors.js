import appApiService, { API_ROUTES } from "../utils/appApiService"

async function getAmbassadors() {
  const participants = await appApiService.getM2Madmin(
    API_ROUTES.participants.all(),
    {
      participantRole: "AMBASSADOR",
    }
  )
  return {
    ambassadors: participants,
  }
}

exports.handler = async (event, context, callback) => {
  // Only allow GET requests
  if (event.httpMethod !== "GET") {
    console.log("Forbidden non-GET request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  try {
    const ambassadorData = await getAmbassadors()
    return callback(null, {
      statusCode: 200,
      body: JSON.stringify(ambassadorData),
    })
  } catch (err) {
    return callback(err, {
      statusCode: 502,
    })
  }
}
