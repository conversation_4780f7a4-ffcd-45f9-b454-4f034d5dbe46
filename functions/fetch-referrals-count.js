import appApiService, { API_ROUTES } from "../utils/appApiService"

async function getReferralsCount(referrerEmail) {
  const { referralsCount } = await appApiService.getM2Madmin(
    API_ROUTES.users.referralsCount(),
    {
      userEmail: referrerEmail,
    }
  )
  return referralsCount
}

exports.handler = async (event, context) => {
  // Only allow GET requests
  if (event.httpMethod !== "GET") {
    console.log("Forbidden non-GET request is made - method not allowed")
    return {
      statusCode: 405,
      body: "Method Not Allowed",
    }
  }

  // TODO: allow only specific domain to make these requests

  try {
    const referrerEmail = event.queryStringParameters.referrerEmail
    const referralsCount = await getReferralsCount(referrerEmail)

    return {
      statusCode: 200,
      body: JSON.stringify({ referralCount: referralsCount }),
    }
  } catch (err) {
    console.log(err)
    return {
      statusCode: 502,
      body: "Failed fetching data",
    }
  }
}
