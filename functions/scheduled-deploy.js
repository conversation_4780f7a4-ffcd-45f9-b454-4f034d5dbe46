import { schedule } from "@netlify/functions"
const axios = require("axios")

const BUILD_HOOK =
  "https://api.netlify.com/build_hooks/667e737d7bad59dc92f89ac7"

// Schedules the handler function to run at 04:00 UTC every night
const handler = schedule("0 4 * * *", async () => {
  try {
    const response = await axios.post(BUILD_HOOK)
    console.log("Build hook response:", response.data)
  } catch (error) {
    console.error("Error triggering build hook:", error)
  }

  return {
    statusCode: 200,
  }
})

export { handler }
