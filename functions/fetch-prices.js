import appApiService, { API_ROUTES } from "../utils/appApiService"

async function getPrices(assetCommonId) {
  const prices = await appApiService.get(API_ROUTES.asset.pricesByTenor(), {
    assetId: assetCommonId,
  })

  return {
    prices,
  }
}

exports.handler = async (event, context, callback) => {
  // Only allow GET requests
  if (event.httpMethod !== "GET") {
    console.log("Forbidden non-GET request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  const assetCommonId = event.queryStringParameters.assetCommonId

  try {
    const priceData = await getPrices(assetCommonId)
    return callback(null, {
      statusCode: 200,
      body: JSON.stringify(priceData),
    })
  } catch (err) {
    return callback(err, {
      statusCode: 502,
    })
  }
}
