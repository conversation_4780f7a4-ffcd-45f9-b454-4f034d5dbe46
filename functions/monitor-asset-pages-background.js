import axios from "axios"
import logger from "../utils/loggerService"
import { captureException } from "@sentry/gatsby"
import {
  publicInvestmentUniverseConfig,
  investmentUniverseConfig,
} from "@wealthyhood/shared-configs"
import RedisService from "../utils/redisService"

const { PublicAssetArrayConst, PUBLIC_ASSET_CONFIG } =
  publicInvestmentUniverseConfig
const { AssetArrayConst } = investmentUniverseConfig

async function verifyAssetPageAvailability(assetPageConfig) {
  const { assetId, url } = assetPageConfig

  try {
    await axios.get(url, {
      timeout: 5000, // 5 seconds
    })
  } catch (err) {
    logger.error(`[${assetId}] Asset page is down`, {
      module: "cron:monitor-asset-pages-background",
      method: "verifyAssetPageAvailability",
      data: {
        assetId,
        url,
        error: err,
      },
    })
  }
}

const DOMAIN_BASE_URL = "https://wealthyhood.com"
const BATCH_SIZE = 20

// Schedules the handler function to run at 05:00 UTC every night
const handler = async () => {
  try {
    logger.info("⏳ Running asset page health checks...", {
      module: "cron:monitor-asset-pages-background",
    })

    const allAssets = [].concat(AssetArrayConst).concat(PublicAssetArrayConst)

    for (let i = 0; i < allAssets.length; i += BATCH_SIZE) {
      const batch = allAssets.slice(i, i + BATCH_SIZE)

      const redisKeys = batch.map(
        (assetCommonId) =>
          `public_asset_data:${PUBLIC_ASSET_CONFIG[assetCommonId].category}:${assetCommonId}`
      )

      const values = await RedisService.Instance.mGet(redisKeys)

      await Promise.all(
        values
          .filter((data, index) => {
            if (!data) {
              logger.error(`No data found in Redis for ${redisKeys[index]}!`, {
                module: "cron:monitor-asset-pages-background",
                method: "handler",
              })
            }

            return !!data
          })
          .map((data) => {
            const assetCommonId = data.key
            const { formalTicker, category } =
              PUBLIC_ASSET_CONFIG[assetCommonId]

            return verifyAssetPageAvailability({
              assetId: assetCommonId,
              url: `${DOMAIN_BASE_URL}/en/${category === "stock" ? "stocks" : "etfs"}/${formalTicker.toLowerCase()}.${data.about.tradingOn.toLowerCase()}?monitor=true`,
            })
          })
      )
    }

    logger.info("✅ Finished running asset page health checks", {
      module: "cron:monitor-asset-pages-background",
    })
    // Add 5 second delay for datadog to receive the logs
    await new Promise((resolve) => setTimeout(resolve, 5000))
  } catch (error) {
    captureException(error)
    logger.error("💔 Finished running asset page health checks", {
      module: "cron:monitor-asset-pages-background",
    })
  }

  return {
    statusCode: 200,
  }
}

export { handler }
