import stockNewsService from "../utils/stockNewsService"
import logger from "../utils/loggerService"

async function getNews(ticker) {
  try {
    const news = await stockNewsService.getStockNews(ticker, 3, "last30days")

    return {
      news,
    }
  } catch (err) {
    logger.error(`Error fetching news for ticker: ${ticker}: ${err.message}`)
  }
}

exports.handler = async (event, context, callback) => {
  // Only allow GET requests
  if (event.httpMethod !== "GET") {
    console.log("Forbidden non-GET request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  const ticker = event.queryStringParameters.ticker

  try {
    const newsData = await getNews(ticker)
    return callback(null, {
      statusCode: 200,
      body: JSON.stringify(newsData),
    })
  } catch (err) {
    return callback(err, {
      statusCode: 502,
    })
  }
}
