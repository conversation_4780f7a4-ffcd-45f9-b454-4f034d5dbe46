import appApiService, { API_ROUTES } from "../utils/appApiService"

async function getParticipantByEmail(email) {
  const participants = await appApiService.getM2Madmin(
    API_ROUTES.participants.all(),
    {
      email,
    }
  )
  return participants[0]
}

exports.handler = async (event, context, callback) => {
  // Only allow GET requests
  if (event.httpMethod !== "GET") {
    console.log("Forbidden non-GET request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  const email = event.queryStringParameters.email
  if (!email) {
    console.log("Email is empty.")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  try {
    const participant = await getParticipantByEmail(email)
    return callback(null, {
      statusCode: 200,
      body: JSON.stringify({ participant }),
    })
  } catch (err) {
    return callback(err, {
      statusCode: 502,
    })
  }
}
