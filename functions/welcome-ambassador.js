import appApiService, { API_ROUTES } from "../utils/appApiService"
const postmark = require("postmark")

const postmarkServerClient = new postmark.ServerClient(
  process.env.POSTMARK_SERVER_API_KEY
)

/**
 * Retrieve participant
 */
async function getParticipant(email) {
  const participants = await appApiService.getM2Madmin(
    API_ROUTES.participants.all(),
    {
      email,
    }
  )
  return participants[0]
}

function sendAffiliateWelcome(recipientMail, wlthdId) {
  const msg = {
    From: "<NAME_EMAIL>",
    To: recipientMail,
    ReplyTo: "<NAME_EMAIL>",
    TemplateId: 19378135,
    TemplateModel: {
      wlthd_id: wlthdId,
      wlthd_email: recipientMail,
    },
  }

  sendEmail(msg, "Welcome Ambassador")
}

function sendEmail(msg, emailType) {
  ;(async () => {
    try {
      await postmarkServerClient.sendEmailWithTemplate(msg)
      console.log(`[${emailType}] sent successfully!`)
    } catch (err) {
      console.log(`[${emailType}] to ${msg.to} failed to sent`)
      console.log(err)
    }
  })()
}

exports.handler = async (event, context, callback) => {
  // 1. Only allow POST requests
  if (event.httpMethod !== "POST") {
    console.log("Forbidden non-POST request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  // 2. Send welcome email
  const body = JSON.parse(event.body)
  const email = body.email

  if (!email) {
    console.log("Email cannot be empty - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Email cannot be empty",
    })
  }

  const affiliate = await getParticipant(email)
  if (affiliate) {
    const { email, wlthdId } = affiliate
    sendAffiliateWelcome(email, wlthdId)
  }

  callback(null, {
    statusCode: 204,
  })
}
