import appApiService, { API_ROUTES } from "../utils/appApiService"

/**
 * Checks if user has already been added to participants
 */
async function checkAmbassadorExists(email) {
  const participants = await appApiService.getM2Madmin(
    API_ROUTES.participants.all(),
    {
      email,
    }
  )
  return participants[0]
}

/**
 * Add new ambassador to referral service
 */
async function addAmbassador(participant) {
  const email = participant.email
  const userFound = await checkAmbassadorExists(email)

  if (userFound) {
    console.log(`ambassador ${email} found - skipping`)
  }

  const participantDoc = await appApiService.postM2Madmin(
    API_ROUTES.participants.all(),
    {
      email,
      participantRole: "AMBASSADOR",
    }
  )

  return participantDoc
}

exports.handler = async (event, context, callback) => {
  // Only allow POST requests
  if (event.httpMethod !== "POST") {
    console.log("Forbidden non-POST request is made - method not allowed")
    return callback(null, {
      statusCode: 405,
      body: "Method Not Allowed",
    })
  }

  const body = JSON.parse(event.body)
  const { email } = body
  const participant = {
    email,
  }

  let ambassador
  try {
    ambassador = await add<PERSON><PERSON><PERSON><PERSON>(participant)
  } catch (err) {
    console.log("** error **")
    console.log(err)
  }

  if (ambassador) {
    console.log(JSON.stringify(ambassador, null, 4))
    return callback(null, {
      statusCode: 204,
    })
  } else {
    return callback(null, {
      statusCode: 502,
    })
  }
}
