{"name": "wealthyhood-landing", "private": true, "description": "Wealthyhood Landing Page", "version": "1.0.3", "author": "<PERSON><PERSON> <<EMAIL>>", "engines": {"node": "20.10.0", "npm": "10.9.0"}, "bugs": {"url": "https://github.com/ksketo/wealthyhood-landing/issues"}, "dependencies": {"@contentful/rich-text-html-renderer": "^16.6.8", "@contentful/rich-text-plain-text-renderer": "^16.2.10", "@contentful/rich-text-types": "^16.8.3", "@datadog/browser-logs": "^5.23.3", "@datadog/browser-rum": "^5.23.3", "@datadog/datadog-api-client": "^1.26.0", "@fortawesome/fontawesome-free": "^6.6.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@netlify/functions": "^2.8.1", "@sentry/gatsby": "^8.33.1", "@upstash/redis": "^1.34.5", "@wealthyhood/shared-configs": "^1.11.12", "analytics-node": "^6.2.0", "animate.css": "^4.1.1", "autoprefixer": "^10.4.19", "axios": "^1.8.2", "better-react-mathjax": "^2.0.3", "bootstrap": "^5.3.3", "chart.js": "^4.4.3", "cookie-universal": "^2.2.2", "crypto-browserify": "^3.12.0", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "firebase-admin": "^12.3.0", "gatsby": "^5.13.7", "gatsby-plugin-feed": "^5.13.1", "gatsby-plugin-google-tagmanager": "^5.13.1", "gatsby-plugin-image": "^3.13.1", "gatsby-plugin-manifest": "^5.13.1", "gatsby-plugin-netlify": "^5.1.1", "gatsby-plugin-offline": "^6.13.2", "gatsby-plugin-react-helmet": "^6.13.1", "gatsby-plugin-sharp": "^5.13.1", "gatsby-plugin-sitemap": "^6.13.1", "gatsby-plugin-typography": "^5.13.1", "gatsby-plugin-web-font-loader": "^1.0.4", "gatsby-remark-copy-linked-files": "^6.13.1", "gatsby-remark-images": "^7.13.1", "gatsby-remark-prismjs": "^7.13.1", "gatsby-remark-responsive-iframe": "^6.13.1", "gatsby-remark-smartypants": "^6.13.1", "gatsby-source-contentful": "^8.13.2", "gatsby-source-filesystem": "^5.13.1", "gatsby-transformer-remark": "^6.13.1", "gatsby-transformer-sharp": "^5.13.1", "lottie-react": "^2.4.0", "marked-react": "^2.0.0", "nanoid": "^5.0.7", "netlify-identity-widget": "^1.9.2", "nouislider-react": "^3.4.2", "pako": "^2.1.0", "postcss": "^8.4.40", "postmark": "^4.0.4", "prismjs": "^1.30.0", "react": "^18.3.1", "react-bootstrap": "^2.10.4", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-scroll": "^1.9.0", "react-slick": "^0.30.2", "react-typography": "^0.16.23", "slick-carousel": "^1.8.1", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.7", "typeface-merriweather": "1.1.13", "typeface-montserrat": "1.1.13", "typography": "^0.16.24", "ua-parser-js": "^1.0.38", "validator": "^13.12.0", "xml2js": "^0.6.2"}, "devDependencies": {"ajv": "^8.17.1", "concurrently": "^9.1.2", "cssnano": "^7.0.6", "postcss-cli": "^11.0.0", "prettier": "^3.5.3"}, "homepage": "https://wealthyhood.com", "keywords": ["wealthyhood", "landing-page", "wealth-management", "invest", "investment-portfolio"], "license": "MIT", "main": "n/a", "repository": {"type": "git", "url": "git+https://github.com/ksketo/wealthyhood-landing.git"}, "scripts": {"build": "gatsby build", "clean": "gatsby clean", "develop": "gatsby develop -H 0.0.0.0 --verbose", "develop:with-clean": "npm run clean && npm run develop", "format": "prettier --write src/**/*.{js,jsx}", "start": "npm run develop", "serve": "gatsby serve", "build:css": "postcss src/styles/style.css -o static/styles/style.css && postcss src/styles/front/theme-3_1.css -o static/styles/front/theme-3_1.css", "watch:css": "postcss src/styles/style.css -o static/styles/style.css --watch & postcss src/styles/front/theme-3_1.css -o static/styles/front/theme-3_1.css --watch", "prestart": "npm run build:css", "prebuild": "npm run build:css", "dev": "npm run build:css && concurrently \"npm run watch:css\" \"npm run develop:with-clean\""}}