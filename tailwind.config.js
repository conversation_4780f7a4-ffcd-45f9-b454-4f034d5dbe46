/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        neutralPrimary: "#11152E",
        darkPrimary: "#171D3F",
        primary: "#536AE3",
        gray: "#F1F3FD",
        darkGray: "#757575",
        neutralGray: "#5D5D5D",
      },
      backgroundImage: {
        "hero-pattern": "url('/static/img/heroBackground.webp')",
      },
      container: {
        center: true,
        padding: "1.5rem",
        screens: {
          xs: "100%", // Full width until small breakpoint
          sm: "640px", // Full width until small breakpoint
          md: "720px", // Fixed width from medium breakpoint
          lg: "960px", // Fixed width from large breakpoint
          xl: "1140px", // Fixed width from extra large breakpoint
          "2xl": "1320px", // Fixed width from 2xl breakpoint
        },
      },
    },
  },
  prefix: "tw-",
  safelist: [
    "tw-rounded-[416px]",
    "tw-border-[#F4F4F4]",
    "tw-border-[rgba(16,19,39,0.05)]",
    "tw-bg-[#F1F3FD]",
    "tw-font-[Noto Sans]",
    "tw-text-[14px]",
    "tw-text-[#15141F]",
    "tw-leading-[1.5em]",
    "tw-max-w-4xl",
    "tw-text-[24px]",
    "tw-text-[20px]",
    "tw-text-[#757575]",
    "tw-z-[1031]",
    "tw-text-[#11152E]",
  ],
  plugins: [],
}
