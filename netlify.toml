[[redirects]]
  from = "/index.html"
  to = "/"
  status = 301
  force = true

# ===============================
# Main Page Redirect
# ===============================
[[redirects]]
  from = "/"
  to = "/uk"
  status = 301
  force = true
  conditions = { Country = ["GB"]}

[[redirects]]
  from = "/"
  to = "/el-gr"
  status = 301
  force = true
  conditions = { Country = ["GR"]}

[[redirects]]
  from = "/"
  to = "/eu"
  status = 301
  force = true

# ===============================
# Referrals Redirects
# ===============================
[[redirects]]
  from = "/referrals"
  to = "/uk/referrals"
  status = 301
  force = true
  conditions = { Country = ["GB"] }

[[redirects]]
  from = "/referrals"
  to = "/el-gr/referrals"
  status = 301
  force = true
  conditions = { Country = ["GR"] }

[[redirects]]
  from = "/referrals"
  to = "/eu/referrals"
  status = 301
  force = true

# ===============================
# Pricing Redirects
# ===============================
[[redirects]]
  from = "/pricing"
  to = "/uk/pricing"
  status = 301
  force = true
  conditions = { Country = ["GB"]}

[[redirects]]
  from = "/pricing"
  to = "/el-gr/pricing"
  status = 301
  force = true
  conditions = { Country = ["GR"]}

[[redirects]]
  from = "/pricing"
  to = "/eu/pricing"
  status = 301
  force = true

# ===============================
# Affiliates Redirects
# ===============================
[[redirects]]
  from = "/affiliates"
  to = "/uk/affiliates"
  status = 301
  force = true
  conditions = { Country = ["GB"]}

[[redirects]]
  from = "/affiliates"
  to = "/eu/affiliates"
  status = 301
  force = true

# ===============================
# Announcements Redirects
# ===============================
[[redirects]]
  from = "/el/announcements/*"
  to = "/el/press-releases/:splat"
  status = 302
  force = true

[[redirects]]
  from = "/en/announcements/*"
  to = "/en/press-releases/:splat"
  status = 302
  force = true

# ===============================
# Removed Pages Redirects
# ===============================
[[redirects]]
  from = "/get-reward"
  to = "/"
  status = 301
  force = true

[[redirects]]
  from = "/referral-dashboard"
  to = "/"
  status = 301
  force = true

# ===============================
# Help Centre Redirects
# ===============================
[[redirects]]
  from = "/help-centre/*"
  to = "/uk/help-centre/:splat"
  status = 302
  force = true
  conditions = { Country = ["GB"] }

[[redirects]]
  from = "/help-centre/*"
  to = "/el-gr/help-centre/:splat"
  status = 302
  force = true
  conditions = { Country = ["GR"] }

[[redirects]]
  from = "/help-centre/*"
  to = "/eu/help-centre/:splat"
  status = 302
  force = true

# ===============================
# ETF Ticker Redirects
# ===============================

[[redirects]]
  from = "/:splat/etfs/sgln"
  to = "/:splat/etfs/sgln.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/isln"
  to = "/:splat/etfs/isln.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/ieaa"
  to = "/:splat/etfs/ieaa.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/slxx"
  to = "/:splat/etfs/slxx.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/lqds"
  to = "/:splat/etfs/lqds.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iash"
  to = "/:splat/etfs/iash.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/verx"
  to = "/:splat/etfs/verx.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vwrl"
  to = "/:splat/etfs/vwrl.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/inrg"
  to = "/:splat/etfs/inrg.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/klwd"
  to = "/:splat/etfs/klwd.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xdwc"
  to = "/:splat/etfs/xdwc.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xdws"
  to = "/:splat/etfs/xdws.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/dgtl"
  to = "/:splat/etfs/dgtl.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/wnrg"
  to = "/:splat/etfs/wnrg.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/wfin"
  to = "/:splat/etfs/wfin.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/cbuf"
  to = "/:splat/etfs/cbuf.xetra"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/heal"
  to = "/:splat/etfs/heal.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xdwi"
  to = "/:splat/etfs/xdwi.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xdwm"
  to = "/:splat/etfs/xdwm.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/bigt"
  to = "/:splat/etfs/bigt.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/smgb"
  to = "/:splat/etfs/smgb.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/eqqu"
  to = "/:splat/etfs/eqqu.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xdwu"
  to = "/:splat/etfs/xdwu.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/esgb"
  to = "/:splat/etfs/esgb.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vjpn"
  to = "/:splat/etfs/vjpn.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/isf"
  to = "/:splat/etfs/isf.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vusa"
  to = "/:splat/etfs/vusa.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/intl"
  to = "/:splat/etfs/intl.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/bnks"
  to = "/:splat/etfs/bnks.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/btek"
  to = "/:splat/etfs/btek.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iucm"
  to = "/:splat/etfs/iucm.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xlyp"
  to = "/:splat/etfs/xlyp.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/icsu"
  to = "/:splat/etfs/icsu.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xlep"
  to = "/:splat/etfs/xlep.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/uifs"
  to = "/:splat/etfs/uifs.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xlvp"
  to = "/:splat/etfs/xlvp.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iisu"
  to = "/:splat/etfs/iisu.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/imsu"
  to = "/:splat/etfs/imsu.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iusz"
  to = "/:splat/etfs/iusz.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/cus1"
  to = "/:splat/etfs/cus1.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/xlkq"
  to = "/:splat/etfs/xlkq.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iusu"
  to = "/:splat/etfs/iusu.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vemt"
  to = "/:splat/etfs/vemt.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vety"
  to = "/:splat/etfs/vety.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vgov"
  to = "/:splat/etfs/vgov.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/vdty"
  to = "/:splat/etfs/vdty.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iwdp"
  to = "/:splat/etfs/iwdp.lse"
  status = 301
  force = true

[[redirects]]
  from = "/:splat/etfs/iusp"
  to = "/:splat/etfs/iusp.lse"
  status = 301
  force = true

# ===============================
# Learning Guides Redirects
# ===============================
[[redirects]]
  from = "/learning-guides/*"
  to = "/uk/learning-guides/:splat"
  status = 302
  force = true
  conditions = { Country = ["GB"] }

[[redirects]]
  from = "/learning-guides/*"
  to = "/el-gr/learning-guides/:splat"
  status = 302
  force = true
  conditions = { Country = ["GR"] }

[[redirects]]
  from = "/learning-guides/*"
  to = "/eu/learning-guides/:splat"
  status = 302
  force = true

# remove chapters
[[redirects]]
  from = "/uk/learning-guides/*/"
  to = "/uk/learning-guides/"
  status = 301
  force = true

[[redirects]]
  from = "/eu/learning-guides/*/"
  to = "/eu/learning-guides/"
  status = 301
  force = true

# ===============================
# Glossary Redirects
# ===============================
[[redirects]]
  from = "/glossary/*"
  to = "/el/glossary/:splat"
  status = 302
  force = true
  conditions = { Country = ["GR"] }

[[redirects]]
  from = "/glossary/*"
  to = "/en/glossary/:splat"
  status = 302
  force = true
  conditions = { Country = ["!GR"] }

[[redirects]]
  from = "/uk/glossary/*"
  to = "/en/glossary/:splat"
  status = 302
  force = true

[[redirects]]
  from = "/eu/glossary/*"
  to = "/en/glossary/:splat"
  status = 302
  force = true

# ===============================
# Stock and ETF Redirects
# ===============================
[[redirects]]
  from = "/eu/stocks/*"
  to = "/en/stocks/:splat"
  status = 301
  force = true

[[redirects]]
  from = "/eu/etfs/*"
  to = "/en/etfs/:splat"
  status = 301
  force = true

[[redirects]]
  from = "/en/stocks/:ticker.:exchange"
  to = "/en/stocks/:ticker.:exchange"
  status = 301
  force = true
  query = {all = ":all"}
  conditions = {Path = {case_sensitive = false}}

[[redirects]]
  from = "/en/etfs/:ticker"
  to = "/en/etfs/:ticker"
  status = 301
  force = true
  query = {all = ":all"}
  conditions = {Path = {case_sensitive = false}}

# ===============================
# Migrated Blog Pages Redirects
# ===============================
[[redirects]]
  from = "/uk/blog/investing/buy-google-shares-in-the-uk-with-no-fees-2024-guide/"
  to = "/uk/blog/investing/buy-google-shares-uk/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-facebook-shares-uk/"
  to = "/uk/blog/investing/buy-meta-shares-uk/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/best-investment-apps-for-beginners/"
  to = "/uk/blog/investing/best-investment-apps-uk/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-amazon-shares/"
  to = "/en/stocks/AMZN.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-nvidia-shares-uk/"
  to = "/en/stocks/NVDA.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-tesla-shares-uk/"
  to = "/en/stocks/TSLA.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-apple-shares/"
  to = "/en/stocks/AAPL.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-meta-shares-uk/"
  to = "/en/stocks/META.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-netflix-shares/"
  to = "/en/stocks/NFLX.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-google-shares-uk/"
  to = "/en/stocks/GOOGL.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-disney-shares-uk/"
  to = "/en/stocks/DIS.NYSE/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-microsoft-shares-uk/"
  to = "/en/stocks/MSFT.NASDAQ/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-uber-shares-uk/"
  to = "/en/stocks/UBER.NYSE/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/investing/buy-bp-shares-uk/"
  to = "/en/stocks/BP.NYSE/"
  status = 302
  force = true

[[redirects]]
  from = "/uk/blog/*"
  to = "/"
  status = 302
  force = true

# ===============================
# Headers
# ===============================
[[headers]]
  for = "/*"
    [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Expose-Headers = "X-User-Region"

[[headers]]
  for = "/styles/*"
  [headers.values]
    Cache-Control = "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0"
    Pragma = "no-cache"
    Expires = "0"

# ===============================
# Build
# ===============================
[build]
  publish = "public"
  command = "gatsby build"
  functions = "./functions"

# ===============================
# Edge Functions
# ===============================
[[edge_functions]]
  path = "/*"
  function = "set-region"

# ===============================
# Functions
# ===============================
[functions]
  node_bundler = "esbuild"

# ===============================
# DEV
# ===============================
[dev]
  targetPort = 8000
